# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.js

# Testing
/coverage

# Next.js build output
/.next
/out

# Next.js internal
.next.lock

# Production build files
/build

# SSR cache files
.next/cache

# Static exports
/export

# Miscellaneous
.DS_Store
*.pem

# Local Environment Variables
.env*.local
.env

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.sublime-workspace

# Optional eslint cache
.eslintcache

# Optional Sentry files
.sentryclirc

# Firebase
firebase-debug.log

# IDX specific
.idx
