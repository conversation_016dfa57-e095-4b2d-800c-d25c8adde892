# ImpulsaTube Pro

## Concepto Central

ImpulsaTube Pro es una plataforma web (con planes de expansión a Android e iOS) diseñada profesionalmente para servir como un puente dinámico entre creadores de contenido de YouTube y una audiencia activa. Su propósito principal es permitir a los creadores de contenido impulsar la visibilidad de sus videos de YouTube a cambio de una moneda virtual, las "Tubecoins". Paralelamente, la plataforma ofrece a los usuarios una forma atractiva y gratificante de ganar Tubecoins al ver videos promocionados y al participar en ofertas de descarga de aplicaciones (offerwall). Este ecosistema crea un ciclo virtuoso donde la audiencia se convierte en un motor de promoción para los creadores, y los creadores recompensan a la audiencia por su tiempo y atención.

## Público Objetivo

Principalmente, creadores de contenido de YouTube (desde principiantes que buscan sus primeras vistas hasta aquellos que desean mantener el engagement) y usuarios interesados en ganar recompensas virtuales de forma lúdica y sencilla.

## Tecnologías Clave

*   **Frontend**: Next.js (TypeScript, Tailwind CSS) - Actualmente. Planes futuros para Flutter para una experiencia web moderna y una futura migración fluida a Android e iOS.
*   **Backend**: Firebase
    *   Firebase Authentication
    *   Cloud Firestore
    *   Cloud Functions for Firebase
    *   Firebase Storage
    *   Firebase Hosting (utilizando App Hosting para despliegue de Next.js)
*   **APIs Externas (Planificadas)**: YouTube Data API, APIs de proveedores de Offerwall/Video Ads.

## Requisitos Funcionales Detallados

### 1. Gestión de Usuarios y Autenticación (Firebase Auth)

*   **RF1.1 Registro de Usuario**:
    *   Permitir registro mediante correo electrónico y contraseña (Firebase Auth).
    *   Integración de inicio de sesión social (Google, Facebook) para facilitar el acceso (Firebase Auth Providers).
    *   Envío y verificación de correo electrónico para activación de cuenta (Firebase Auth).
*   **RF1.2 Inicio de Sesión**:
    *   Credenciales de correo/contraseña.
    *   Funcionalidad "Recordarme".
    *   Flujo de recuperación de contraseña.
*   **RF1.3 Perfil de Usuario**:
    *   Visualización y edición de datos del perfil (nombre de usuario, foto de perfil (Firebase Storage), biografía, etc.) – Datos almacenados en Cloud Firestore.
    *   Visualización del saldo actual de Tubecoins del usuario (en tiempo real desde Cloud Firestore).
    *   Historial detallado de todas las transacciones de Tubecoins (ganadas por ver videos/ofertas, gastadas en campañas) – Almacenado en una subcolección de Firestore.
*   **RF1.4 Dashboard Personalizado**:
    *   Para creadores: Resumen visual de campañas activas, Tubecoins disponibles, estadísticas clave (vistas generadas, gasto) – Consultas a Firestore.
    *   Para espectadores: Resumen de Tubecoins ganadas, progreso en rankings, ofertas destacadas disponibles.

### 2. Gestión de Videos y Campañas (Firestore & Cloud Functions)

*   **RF2.1 Añadir Video de YouTube**:
    *   Input de URL de YouTube.
    *   Cloud Function que valida la URL y utiliza la YouTube Data API para extraer automáticamente el título, descripción, miniatura y duración del video.
    *   Almacenar metadatos del video en Cloud Firestore.
*   **RF2.2 Crear Campaña de Impulso**:
    *   El creador define las "Tubecoins por vista" que está dispuesto a pagar.
    *   El creador establece un "presupuesto total" de Tubecoins para la campaña.
    *   Opcional: Establecer un límite de vistas diarias o un gasto diario máximo.
    *   Opcional: Selección de categorías para el video (ej., juegos, música, educación) para una mejor segmentación de la audiencia.
    *   Los datos de la campaña se almacenan en Cloud Firestore.
*   **RF2.3 Gestionar Campañas**:
    *   Visualización de un listado de todas las campañas del creador (activas, pausadas, completadas).
    *   Acciones: Pausar, reanudar o finalizar una campaña activa.
    *   Funcionalidad para añadir más Tubecoins a una campaña en curso.
    *   Notificaciones sobre el estado de la campaña (ej., "Campaña casi sin Tubecoins", "Campaña completada").
*   **RF2.4 Estadísticas Detalladas de Campañas**:
    *   Visualización clara del progreso de cada campaña (vistas recibidas vs. objetivo, Tubecoins gastadas vs. presupuesto).
    *   Gráficos de rendimiento (vistas diarias, gasto diario de Tubecoins).
    *   Métricas clave (costo efectivo por vista, tiempo de visualización promedio si se obtiene de YouTube Analytics).

### 3. Exploración y Visualización de Videos (Firestore & Cloud Functions)

*   **RF3.1 Listado de Videos para Ver**:
    *   Página principal que muestra una lista dinámica de videos disponibles para ganar Tubecoins.
    *   Cada entrada de video muestra miniatura, título, duración y la cantidad de Tubecoins a ganar.
    *   Funcionalidad de Filtrado: Por categoría, duración, Tubecoins ofrecidas, más recientes, populares.
    *   Funcionalidad de Búsqueda: Búsqueda de videos por título o creador.
*   **RF3.2 Reproductor de Video Integrado**:
    *   El video de YouTube se incrusta y reproduce directamente en la aplicación web.
    *   Mecanismo de Recompensa Seguro: Al cumplir un umbral de tiempo de visualización predefinido (ej., 30 segundos, o el 80% del video), el cliente (Next.js/Flutter) llama a una Cloud Function. Esta Cloud Function valida la acción (evitando bots/trampas) y realiza una transacción atómica en Firestore para:
        *   Restar las Tubecoins del saldo del creador de la campaña.
        *   Acreditar las Tubecoins al saldo del espectador.
        *   Registrar la vista para la campaña del creador.
*   **RF3.3 Notificación de Recompensa**:
    *   Feedback instantáneo al usuario cuando gana Tubecoins (ej., un Toast, Snackbar o animación).
    *   Actualización en tiempo real del saldo de Tubecoins en el UI.

### 4. Offerwall y Monetización (Cloud Functions como Intermediarios)

*   **RF4.1 Integración de Offerwall (Descargas de Apps)**:
    *   Sección dedicada con un listado de ofertas de descarga de aplicaciones.
    *   Cada oferta detalla los pasos a seguir, las condiciones y la recompensa en Tubecoins.
    *   Una Cloud Function servirá como endpoint para los "callbacks" o "webhooks" de los proveedores de offerwall. Cuando una oferta se complete y el proveedor notifique a la Cloud Function, esta validará y acreditará las Tubecoins al usuario en Cloud Firestore.
*   **RF4.2 Integración de Video Ads Recompensados**:
    *   Opción para que los usuarios vean anuncios de video cortos (ej., 15-30 segundos) a cambio de una cantidad fija de Tubecoins.
    *   La integración se realizará a través del SDK del proveedor o a través de Cloud Functions que manejen la lógica de validación de visualización completa y acreditación de Tubecoins.
*   **RF4.3 Compra Directa de Tubecoins (Monetización Premium)**:
    *   Sección de la tienda para que los usuarios puedan comprar Tubecoins con dinero real.
    *   Integración con pasarelas de pago seguras (ej., Stripe, PayPal).
    *   Una Cloud Function actuará como el "backend seguro" para procesar estas transacciones, comunicarse con la pasarela de pago y, tras la confirmación, acreditar las Tubecoins al usuario en Cloud Firestore.

### 5. Ranking y Gamificación (Firestore & Cloud Functions)

*   **RF5.1 Ranking de "Influencers del Día/Semana/Mes"**:
    *   Listado de los creadores más activos o exitosos (ej., mayor cantidad de Tubecoins gastadas en impulsar, más vistas generadas).
    *   Los datos del ranking se calcularán periódicamente mediante Cloud Functions o se mantendrán actualizados en tiempo real mediante triggers de Firestore, almacenándose en una colección dedicada de Firestore para lecturas rápidas.
*   **RF5.2 Ranking de "Espectadores Estrella"**:
    *   Clasificación de los usuarios que más Tubecoins han ganado en un período determinado (viendo videos y completando ofertas).
    *   Similar al ranking de influencers en su implementación con Firestore y Cloud Functions.
*   **RF5.3 Sistema de Insignias y Logros**:
    *   Otorga insignias virtuales a los usuarios y creadores por alcanzar hitos (ej., "Primera Campaña Exitosa", "1000 Vistas Recibidas", "50 Ofertas Completadas", "Top 10 Espectador").
    *   El progreso y las insignias desbloqueadas se almacenan en Firestore.

### 6. Administración y Herramientas de Backend (Firebase Console & Custom UI)

*   **RF6.1 Gestión de Usuarios**: Funcionalidad para ver, editar, suspender o eliminar cuentas de usuario. Acceso básico desde Firebase Console, o un panel de administración personalizado con Cloud Functions para acciones privilegiadas.
*   **RF6.2 Monitoreo de Contenido y Campañas**: Herramientas para que los administradores revisen videos, detecten contenido inapropiado y supervisen el rendimiento de las campañas. Directamente en Cloud Firestore y los logs de Cloud Functions.
*   **RF6.3 Configuración de Offerwalls/Ads**: Interfaz (o acceso a consolas de terceros) para configurar y monitorear la integración con proveedores de monetización.
*   **RF6.4 Auditoría de Transacciones**: Visualización de un registro completo de todas las transacciones de Tubecoins para fines de auditoría y soporte.
*   **RF6.5 Reportes y Analíticas**: Generación de informes básicos sobre el uso de la aplicación, métricas de monetización, usuarios activos, etc. (Usando Google Analytics para Firebase y/o consultas personalizadas con Cloud Functions).

## Ideas Futuras a Integrar

*   **Moneda "Premium" Adicional**: Introducir "ProPoints" (compra con dinero real o eventos especiales) para desbloquear características exclusivas o beneficios.
*   **Sistema de Referidos**: Ganar Tubecoins o ProPoints por invitar nuevos usuarios activos.
*   **Chat/Mensajería Directa**: Interacción entre usuarios y creadores.
*   **Desafíos de Creación de Contenido**: Eventos temáticos con recompensas.
*   **Eventos y Torneos**: "Doble Tubecoins", "Maratones de Vistas".
*   **Integración con YouTube Analytics (Más Profunda)**: Vincular cuenta de YouTube (OAuth) para métricas avanzadas en ImpulsaTube Pro.
*   **Mercado de Servicios**: Usuarios ofrecen servicios relacionados con YouTube a cambio de Tubecoins.
*   **Niveles de Usuario / Club VIP**: Beneficios por actividad y nivel.
*   **Alertas y Notificaciones Personalizadas**: Push (móvil), in-app/email.
*   **Internacionalización (i18n)**: Soporte para múltiples idiomas.

## Estado Actual del Proyecto

*   Frontend web básico implementado con Next.js, TypeScript y Tailwind CSS.
*   Autenticación con Google y Facebook funcional (Firebase Auth).
*   Mock login con email/password implementado para pruebas.
*   Páginas principales (Home, Login, Dashboard, AI Suggestions) creadas.
*   Despliegue a Firebase App Hosting configurado y funcional.
*   El backend con Firebase (Firestore, Cloud Functions) está en desarrollo para las funcionalidades principales.

## Próximos Pasos (Desarrollo)

1.  **Completar implementación de Autenticación**: Añadir registro y login con email/password real usando Firebase Auth. Manejo de errores completo y flujo de verificación de email.
2.  **Desarrollar Módulo de Perfil de Usuario**: Visualización/edición de perfil, subida de avatar a Firebase Storage, visualización de saldo de Tubecoins.
3.  **Implementar Lógica de Tubecoins**: Creación de la colección de transacciones en Firestore.
4.  **Desarrollar Funcionalidad de Campañas (Creador)**:
    *   Cloud Function para validar URL de YouTube y obtener metadatos.
    *   Formulario para crear campaña (definir Tubecoins/vista, presupuesto).
    *   Listado y gestión de campañas.
5.  **Desarrollar Funcionalidad de Visualización (Espectador)**:
    *   Listado de videos de campañas activas.
    *   Reproductor de video integrado.
    *   Cloud Function segura para validar visualización y acreditar Tubecoins.

## Despliegue

El proyecto está desplegado en Firebase App Hosting.

*   **URL de Despliegue**: [https://impulsatubeprobackend--portafoliodev-bd7f3.us-central1.hosted.app](https://impulsatubeprobackend--portafoliodev-bd7f3.us-central1.hosted.app)

Para desplegar nuevas versiones:

1.  Asegúrese de que todos los cambios estén confirmados en git.
2.  Ejecute el comando: `firebase deploy`

## Contribuir

(Sección a detallar si el proyecto se abre a colaboraciones)
