# Settings for Backend (on Cloud Run).
# See https://firebase.google.com/docs/app-hosting/configure#cloud-run
runConfig:
  minInstances: 0
  # maxInstances: 100
  # concurrency: 80
  # cpu: 1
  # memoryMiB: 512

# Environment variables and secrets.
# See https://firebase.google.com/docs/app-hosting/configure#user-defined-environment
env:
  - variable: NEXT_PUBLIC_FIREBASE_API_KEY
    value: AIzaSyCe0hxV1Vxnq_yH_l4cUH-m75vXDlgcCAc
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
    value: portafoliodev-bd7f3.firebaseapp.com
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_FIREBASE_PROJECT_ID
    value: portafoliodev-bd7f3
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET
    value: portafoliodev-bd7f3.appspot.com
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID
    value: "715605817338"
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_FIREBASE_APP_ID
    value: 1:715605817338:web:0e7cea48051428e29f5643
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
    value: G-X2X61B2F3P
    availability:
      - BUILD
      - RUNTIME
  # Example of a non-public variable (if you had one, not directly used by client-side Firebase config)
  # - variable: SOME_BACKEND_SECRET 
  #   value: "itsasecret"
  #   availability:
  #     - RUNTIME # Or BUILD if needed by build process but not client

  # Grant access to secrets in Cloud Secret Manager.
  # See https://firebase.google.com/docs/app-hosting/configure#secret-parameters
  # - variable: MY_SECRET
  #   secret: mySecretRef
