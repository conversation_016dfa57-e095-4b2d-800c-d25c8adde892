import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, Eye, Upload, Star, Coins } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"

export function StatsCards() {
  const { user } = useAuth();
  const totalEarned = user?.totalEarned || 0;
  const videosWatched = user?.videosWatched || 0;
  const videosPromoted = user?.videosPromoted || 0;
  const offersCompleted = user?.offersCompleted || 0;
  const currentBalance = user?.tubeCoins || 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card className="relative overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Current Balance</CardTitle>
          <Coins className="h-4 w-4 text-yellow-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-600">{currentBalance}</div>
          <p className="text-xs text-muted-foreground">Tubecoins available</p>
          <Progress value={Math.min((currentBalance / 1000) * 100, 100)} className="mt-2" />
          <div className="flex items-center gap-1 mt-2">
            <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800">
              Ready to spend
            </Badge>
          </div>
        </CardContent>
      </Card>

      <Card className="relative overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Videos Watched</CardTitle>
          <Eye className="h-4 w-4 text-blue-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">{videosWatched}</div>
          <p className="text-xs text-muted-foreground">+{Math.floor(videosWatched * 0.3)} this week</p>
          <Progress value={Math.min((videosWatched / 50) * 100, 100)} className="mt-2" />
          <div className="flex items-center gap-1 mt-2">
            <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">
              +{videosWatched * 10} Tubecoins earned
            </Badge>
          </div>
        </CardContent>
      </Card>

      <Card className="relative overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Videos Promoted</CardTitle>
          <Upload className="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{videosPromoted}</div>
          <p className="text-xs text-muted-foreground">{videosPromoted} active campaigns</p>
          <Progress value={Math.min((videosPromoted / 10) * 100, 100)} className="mt-2" />
          <div className="flex items-center gap-1 mt-2">
            <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
              {videosPromoted * 150} total views
            </Badge>
          </div>
        </CardContent>
      </Card>

      <Card className="relative overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Offers Completed</CardTitle>
          <Star className="h-4 w-4 text-purple-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-purple-600">{offersCompleted}</div>
          <p className="text-xs text-muted-foreground">+{Math.floor(offersCompleted * 0.2)} this week</p>
          <Progress value={Math.min((offersCompleted / 20) * 100, 100)} className="mt-2" />
          <div className="flex items-center gap-1 mt-2">
            <Badge variant="secondary" className="text-xs bg-purple-100 text-purple-800">
              +{offersCompleted * 25} Tubecoins earned
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
