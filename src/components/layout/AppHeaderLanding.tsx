
"use client";

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Youtube } from 'lucide-react'; // Or your preferred logo icon

export const AppHeaderLanding = () => {
  const router = useRouter();

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-slate-900/80 backdrop-blur-md shadow-md">
      <div className="container mx-auto px-4 md:px-6 h-16 flex items-center justify-between">
        {/* Logo / Brand Name */}
        <Link href="/" legacyBehavior>
          <a className="flex items-center space-x-2 text-xl font-bold text-white hover:text-primary transition-colors">
            <Youtube className="h-7 w-7 text-primary" /> 
            <span>ImpulsaTube Pro</span>
          </a>
        </Link>

        {/* Navigation Links Removed for now - page content will cover these */}
        {/* <nav className="hidden md:flex items-center space-x-6">
          <Link href="#how-it-works" legacyBehavior> 
            <a className="text-slate-300 hover:text-primary transition-colors">How It Works</a>
          </Link>
          <Link href="#features" legacyBehavior>
            <a className="text-slate-300 hover:text-primary transition-colors">Features</a>
          </Link>
        </nav> */}

        {/* Auth Button */}
        <div>
          <Button 
            onClick={() => router.push('/login')}
            variant="default"
            className="bg-primary hover:bg-primary/90 text-slate-900 font-semibold px-6 py-2 rounded-md"
          >
            Log In / Sign Up
          </Button>
        </div>
      </div>
    </header>
  );
};
