
"use client";

import { useEffect, useRef, useState } from "react";
import { motion, useInView, animate } from "framer-motion";

interface AnimatedStatProps {
  targetValue: number;
  text?: string; // For suffixes like "+" or "M+"
  duration?: number;
  className?: string;
}

export const AnimatedStat: React.FC<AnimatedStatProps> = ({
  targetValue,
  text = "",
  duration = 1.5, // Duration in seconds
  className = "",
}) => {
  const ref = useRef<HTMLSpanElement>(null);
  const isInView = useInView(ref, { once: true, margin: "-50px 0px" });
  const [currentValue, setCurrentValue] = useState(0);

  useEffect(() => {
    if (isInView) {
      const controls = animate(0, targetValue, {
        duration: duration,
        ease: "easeOut",
        onUpdate(latest) {
          setCurrentValue(Math.round(latest));
        },
      });
      return () => controls.stop();
    }
  }, [isInView, targetValue, duration]);

  return (
    <span ref={ref} className={className}>
      {currentValue.toLocaleString()}{text}
    </span>
  );
};
