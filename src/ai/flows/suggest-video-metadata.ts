'use server';
/**
 * @fileOverview This file defines a Genkit flow that suggests video descriptions and tags
 * based on the video content.
 *
 * - suggestVideoMetadata - A function that suggests video descriptions and tags.
 * - SuggestVideoMetadataInput - The input type for the suggestVideoMetadata function.
 * - SuggestVideoMetadataOutput - The output type for the suggestVideoMetadata function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const SuggestVideoMetadataInputSchema = z.object({
  videoContentDescription: z
    .string()
    .describe('A description of the video content.'),
  targetAudience: z
    .string()
    .describe('The target audience for the video.'),
  keywords: z
    .string()
    .describe('Keywords related to the video content.'),
});

export type SuggestVideoMetadataInput = z.infer<typeof SuggestVideoMetadataInputSchema>;

const SuggestVideoMetadataOutputSchema = z.object({
  suggestedDescription: z
    .string()
    .describe('A suggested video description.'),
  suggestedTags: z.array(z.string()).describe('Suggested video tags.'),
});

export type SuggestVideoMetadataOutput = z.infer<typeof SuggestVideoMetadataOutputSchema>;

export async function suggestVideoMetadata(
  input: SuggestVideoMetadataInput
): Promise<SuggestVideoMetadataOutput> {
  return suggestVideoMetadataFlow(input);
}

const suggestVideoMetadataPrompt = ai.definePrompt({
  name: 'suggestVideoMetadataPrompt',
  input: {schema: SuggestVideoMetadataInputSchema},
  output: {schema: SuggestVideoMetadataOutputSchema},
  prompt: `You are an expert in creating engaging video descriptions and relevant tags to maximize video discoverability.

  Based on the following video information, please generate a suggested video description and a list of suggested tags.

  Video Content Description: {{{videoContentDescription}}}
  Target Audience: {{{targetAudience}}}
  Existing Keywords: {{{keywords}}}

  Description should be concise and appealing to the target audience.
  Tags should be highly relevant keywords to improve search rankings.

  Ensure the description accurately reflects the video content and entices viewers to watch.
  Tags should cover a range of relevant search terms, including specific topics, related themes, and common misspellings.

  Format the suggested tags as a JSON array of strings.
  `,
});

const suggestVideoMetadataFlow = ai.defineFlow(
  {
    name: 'suggestVideoMetadataFlow',
    inputSchema: SuggestVideoMetadataInputSchema,
    outputSchema: SuggestVideoMetadataOutputSchema,
  },
  async input => {
    const {output} = await suggestVideoMetadataPrompt(input);
    return output!;
  }
);
