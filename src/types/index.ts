import type { User as FirebaseUser } from "firebase/auth";
import type { Timestamp } from "firebase/firestore";

export interface UserProfile extends FirebaseUser {
  tubeCoins?: number;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
  // Additional user stats for dashboard
  totalEarned?: number;
  videosWatched?: number;
  videosPromoted?: number;
  offersCompleted?: number;
  referralsCount?: number;
  level?: number;
  badges?: string[];
}

export interface Campaign {
  id: string;
  creatorId: string;
  videoUrl: string;
  youtubeVideoId?: string;
  title: string;
  description?: string;
  tags?: string[];
  coinsPerView: number;
  totalBudget: number;
  remainingBudget: number;
  views: number;
  isActive: boolean;
  createdAt: Date;
  thumbnailUrl?: string;
  category?: string;
  duration?: string;
  likes?: number;
  dislikes?: number;
  comments?: number;
  reward?: number; // Alias for coinsPerView for compatibility
}

export interface Video {
  id: string;
  userId: string;
  youtubeUrl: string;
  title: string;
  description?: string;
  tags?: string[];
  thumbnail?: string;
  duration?: string;
  views?: string;
  category?: string;
  channel?: string;
  channelAvatar?: string;
  likes?: number;
  dislikes?: number;
  comments?: number;
  reward?: number;
}

export interface Offer {
  id: string;
  title: string;
  description: string;
  rewardAmount: number; // TubeCoins
  provider: string; // e.g., "OfferToro", "AdGem"
  imageUrl?: string;
  actionUrl: string;
  category?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  estimatedTime?: string;
  requirements?: string[];
}

export interface Transaction {
  id: string;
  userId: string;
  type: 'earned' | 'spent' | 'purchased' | 'referral' | 'bonus';
  amount: number;
  description: string;
  source?: string; // video title, offer name, etc.
  sourceId?: string; // campaign id, offer id, etc.
  createdAt: Timestamp;
  status: 'pending' | 'completed' | 'failed';
}

export interface Referral {
  id: string;
  referrerId: string;
  referredUserId?: string;
  referredEmail?: string;
  code: string;
  status: 'pending' | 'completed';
  reward: number;
  createdAt: Timestamp;
  completedAt?: Timestamp;
}

export interface OfferwallProvider {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  textColor: string;
  bgColor: string;
  stats: {
    totalOffers: number;
    avgPayout: number;
    completionRate: number;
    rating: number;
  };
  features: string[];
  iframeUrl: string;
  isActive: boolean;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'earning' | 'watching' | 'promoting' | 'social' | 'special';
  requirement: number;
  reward: number;
  isUnlocked: boolean;
  unlockedAt?: Timestamp;
}

export interface LeaderboardEntry {
  userId: string;
  displayName: string;
  photoURL?: string;
  score: number; // Could be TubeCoins earned for viewers, or TubeCoins spent/views generated for influencers
  rank: number;
  change?: number; // Position change from previous period
}

export interface PurchasePackage {
  id: string;
  name: string;
  tubecoins: number;
  price: number;
  currency: string;
  popular?: boolean;
  bonus?: number;
  description?: string;
}
