
"use client";

import type { ReactNode } from "react";
import { useRouter } from "next/navigation";
import React, { createContext, useContext, useEffect, useState } from "react";
import {
  onAuthStateChanged,
  signOut as firebaseSignOut,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword as firebaseSignInWithEmailAndPassword,
  sendPasswordResetEmail as firebaseSendPasswordResetEmail,
  sendEmailVerification as firebaseSendEmailVerification,
  applyActionCode,
  updateProfile as firebaseUpdateProfile,
  type User as FirebaseUser,
  type UserCredential,
  type UserInfo,
  type UserMetadata,
} from "firebase/auth";
import { auth, db } from "@/lib/firebase/config";
import type { UserProfile } from "@/types";
import { doc, getDoc, setDoc, Timestamp } from "firebase/firestore";
import { Loader2 } from "lucide-react";
import { usePathname } from 'next/navigation'; 

const PROJECT_NAME = "impulsatubepro";

interface AuthContextType {
  user: UserProfile | null;
  loading: boolean; 
  signUpWithEmailAndPassword: (email: string, password: string, displayName: string) => Promise<FirebaseUser | null>; 
  signInWithEmailAndPassword: (email: string, password: string) => Promise<FirebaseUser | null>; 
  sendPasswordReset: (email: string) => Promise<void>; 
  sendEmailVerification: (user: FirebaseUser) => Promise<void>;
  verifyEmail: (oobCode: string) => Promise<boolean>;
  signOut: () => Promise<void>;
  tubeCoins: number;
  setTubeCoins: React.Dispatch<React.SetStateAction<number>>;
  fetchTubeCoins: () => Promise<void>;
  setMockUser?: (user: UserProfile | null) => void; 
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  // User state with proper typing
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true); // Reflects onAuthStateChanged processing
  const [tubeCoins, setTubeCoins] = useState<number>(0);
  const [hasMounted, setHasMounted] = useState(false);
  const pathname = usePathname();
  
  // Helper function to map Firebase User to our UserProfile
  const mapFirebaseUserToProfile = async (firebaseUser: FirebaseUser | null): Promise<UserProfile | null> => {
    if (!firebaseUser) return null;
    
    try {
      // Get additional user data from Firestore
      const userDoc = await getDoc(doc(db, getUserDocPath(firebaseUser.uid)));
      const userData = userDoc.data();
      
      // Create the user profile object with proper typing
      const userProfile: UserProfile = {
        // Firebase User properties
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        emailVerified: firebaseUser.emailVerified,
        isAnonymous: firebaseUser.isAnonymous,
        metadata: firebaseUser.metadata,
        providerData: firebaseUser.providerData,
        refreshToken: firebaseUser.refreshToken,
        tenantId: firebaseUser.tenantId,
        
        // Custom fields with defaults
        displayName: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
        photoURL: firebaseUser.photoURL,
        phoneNumber: firebaseUser.phoneNumber || null,
        tubeCoins: userData?.tubeCoins || 0,
        createdAt: userData?.createdAt || Timestamp.now(),
        updatedAt: userData?.updatedAt || Timestamp.now(),
        
        // Firebase User methods
        delete: firebaseUser.delete.bind(firebaseUser),
        getIdToken: firebaseUser.getIdToken.bind(firebaseUser),
        getIdTokenResult: firebaseUser.getIdTokenResult.bind(firebaseUser),
        reload: firebaseUser.reload.bind(firebaseUser),
        toJSON: firebaseUser.toJSON.bind(firebaseUser),
        providerId: firebaseUser.providerId,
      };
      
      return userProfile;
    } catch (error) {
      console.error("Error mapping user profile:", error);
      return null;
    }
  }; 

  useEffect(() => {
    setHasMounted(true);
  }, []);

  const getUserDocPath = (uid: string) => `projects/${PROJECT_NAME}/users/${uid}`;

  useEffect(() => {
    if (!hasMounted) return; 
    console.log("AuthContext: useEffect [hasMounted] running for onAuthStateChanged.");

    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
      console.log("AuthContext: onAuthStateChanged triggered. User:", firebaseUser?.uid);
      setLoading(true); 
      
      try {
        if (firebaseUser) {
          // Map Firebase User to our UserProfile
          const userProfile = await mapFirebaseUserToProfile(firebaseUser);
          
          if (!userProfile) {
            console.error("Failed to map user profile");
            setUser(null);
            setLoading(false);
            return;
          }
          
          // Create user document in Firestore if it doesn't exist
          const userRef = doc(db, getUserDocPath(firebaseUser.uid));
          const userSnap = await getDoc(userRef);
          
          if (!userSnap.exists()) {
            console.log("AuthContext: Creating Firestore doc for user:", firebaseUser.uid);
            await setDoc(userRef, {
              uid: firebaseUser.uid,
              displayName: userProfile.displayName,
              email: firebaseUser.email,
              photoURL: firebaseUser.photoURL,
              tubeCoins: 0,
              createdAt: Timestamp.now(),
              updatedAt: Timestamp.now(),
            });
          }
          
          // Update user state
          setUser(userProfile);
          setTubeCoins(userProfile.tubeCoins || 0);
          
          console.log("AuthContext: User profile loaded:", userProfile.uid);
        } else {
          // User signed out
          setUser(null);
          setTubeCoins(0);
          console.log("AuthContext: User signed out");
        }
      } catch (error) {
        console.error("Error in auth state change:", error);
        setUser(null);
        setTubeCoins(0);
      } finally {
        setLoading(false);
      }
    });
    
    return () => {
      console.log("AuthContext: Cleaning up onAuthStateChanged listener.");
      unsubscribe();
    }
  }, [hasMounted]); 
  
  const signUpWithEmailAndPassword = async (email: string, password: string, displayName: string): Promise<FirebaseUser | null> => {
    try {
      console.log("AuthContext: signUpWithEmailAndPassword called with email:", email);
      setLoading(true);
      
      // Create user with email and password
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      
      if (!firebaseUser) {
        throw new Error("Failed to create user");
      }
      
      // Update user profile with display name
      await firebaseUpdateProfile(firebaseUser, { displayName });
      
      // Send email verification
      await firebaseSendEmailVerification(firebaseUser);
      
      // Create user document in Firestore with only custom fields
      const userRef = doc(db, getUserDocPath(firebaseUser.uid));
      const userData = {
        tubeCoins: 0,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      };
      
      await setDoc(userRef, userData);
      
      // Force a refresh of the user to get the latest data
      await firebaseUser.reload();
      
      // Map to our UserProfile
      const userProfile = await mapFirebaseUserToProfile(firebaseUser);
      if (!userProfile) {
        throw new Error("Failed to map user profile");
      }
      
      // Update local state
      setUser(userProfile);
      setTubeCoins(0);
      
      return firebaseUser;
    } catch (error) {
      console.error("Error signing up:", error);
      throw error;
    }
  };

  const signInWithEmailAndPassword = async (email: string, password: string): Promise<FirebaseUser | null> => { 
    try { 
      setLoading(true);
      console.log("AuthContext: signInWithEmailAndPassword called with email:", email);
      
      const userCredential = await firebaseSignInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      
      if (!firebaseUser) {
        throw new Error("Failed to sign in");
      }
      
      // Map to our UserProfile
      const userProfile = await mapFirebaseUserToProfile(firebaseUser);
      if (!userProfile) {
        throw new Error("Failed to map user profile");
      }
      
      // Update local state
      setUser(userProfile);
      setTubeCoins(userProfile.tubeCoins || 0);
      
      return firebaseUser;
    } catch (error) {
      console.error("Error signing in:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  }; 

  const sendPasswordReset = async (email: string): Promise<void> => { 
    // Page-level isSendingResetEmail state will handle UI.
    try {
      await firebaseSendPasswordResetEmail(auth,email)
    }catch(e){
      console.error("AuthContext ResetErr:",e);
      throw e;
    }
  }; 
  
  const sendEmailVerification = async (user: FirebaseUser): Promise<void> => {
    try {
      await firebaseSendEmailVerification(user, {
        url: `${window.location.origin}/verify-email`,
        handleCodeInApp: true,
      });
    } catch (error) {
      console.error("Error sending verification email:", error);
      throw error;
    }
  };

  const verifyEmail = async (oobCode: string): Promise<boolean> => {
    try {
      await applyActionCode(auth, oobCode);
      
      // Update local user state if needed
      if (user && auth.currentUser) {
        // Refresh the user's token to get the latest email verification status
        await auth.currentUser.reload();
        const updatedUser = auth.currentUser;
        
        setUser({
          ...user,
          emailVerified: updatedUser.emailVerified,
        });
      }
      
      return true;
    } catch (error) {
      console.error("Error verifying email:", error);
      return false;
    }
  };

  const router = useRouter();
  
  const signOut = async () => { 
    // setLoading(true); // Let onAuthStateChanged handle loading state
    try {
      await firebaseSignOut(auth);
      // Redirigir a la página de inicio después de cerrar sesión
      router.push('/');
      // onAuthStateChanged will set user to null and loading to false
    }catch(e){
      console.error("AuthContext SignOutErr:",e);
      // If signOut itself errors, onAuthStateChanged might not fire to set loading false.
      // However, typically it still results in user being null.
      // For safety, ensure loading is reset if there's an error directly in signOut.
      setLoading(false);
    }
  }; 
  
  const setMockUser = (mockUser: UserProfile | null) => {
    if (mockUser) {
      // Crear un objeto con los valores por defecto para las propiedades requeridas
      const defaultUserProfile: Partial<UserProfile> = {
        emailVerified: false,
        isAnonymous: false,
        metadata: {} as any,
        providerData: [],
        refreshToken: '',
        tenantId: null,
        tubeCoins: 0,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        displayName: null,
        photoURL: null,
        phoneNumber: null,
      };
      
      // Combinar con el mockUser proporcionado
      const userProfile: UserProfile = {
        ...defaultUserProfile,
        ...mockUser,
        // Asegurar que las fechas sean Timestamp
        createdAt: mockUser.createdAt || Timestamp.now(),
        updatedAt: mockUser.updatedAt || Timestamp.now(),
      };
      
      setUser(userProfile);
      setTubeCoins(mockUser.tubeCoins || 0);
    } else {
      setUser(null);
      setTubeCoins(0);
    }
    setTimeout(()=>setLoading(false),50)
  };

  const fetchTubeCoins = async () => { /* unchanged */ };

  // Loader display logic
  if (!hasMounted || loading) { 
    const isAuthPage = pathname?.startsWith("/(auth)");
    if (isAuthPage && !user && loading) { 
    } else if (loading) { 
      return (
        <div className="flex h-screen w-screen items-center justify-center bg-background">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
        </div>
      );
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        loading: hasMounted ? loading : true, // Show loading until mounted on client
        signUpWithEmailAndPassword,
        signInWithEmailAndPassword,
        sendPasswordReset,
        sendEmailVerification,
        verifyEmail,
        signOut,
        tubeCoins,
        setTubeCoins,
        fetchTubeCoins,
        setMockUser: process.env.NODE_ENV === 'development' ? setUser : undefined, // Only expose in dev
      }}
    >
      {!hasMounted ? (
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
