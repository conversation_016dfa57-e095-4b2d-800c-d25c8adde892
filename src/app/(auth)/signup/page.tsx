
"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Youtube, AlertTriangle, ShieldCheck } from "lucide-react";
import Link from 'next/link';
import { FirebaseError } from 'firebase/app'; // For typed error checking

export default function SignUpPage() {
  const { user, signUpWithEmailAndPassword, loading: authLoading } = useAuth();
  const router = useRouter();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSigningUp, setIsSigningUp] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      router.replace('/dashboard'); 
    }
  }, [user, router]);

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccessMessage(null);

    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      return;
    }
    if (password.length < 6) {
        setError("Password should be at least 6 characters.");
        return;
    }

    setIsSigningUp(true);
    try {
      await signUpWithEmailAndPassword(email, password, displayName || email.split('@')[0]);
      setSuccessMessage("Account created successfully! You will be redirected shortly. Please check your email to verify your account.");
    } catch (err: any) {
      if (err instanceof FirebaseError) {
        switch (err.code) {
          case 'auth/email-already-in-use':
            setError("This email address is already in use. Please log in or use a different email address.");
            break;
          case 'auth/weak-password':
            setError("The password is too weak. Please choose a stronger password with at least 6 characters.");
            break;
          case 'auth/invalid-email':
            setError("The email address is not valid. Please enter a valid email address.");
            break;
          case 'auth/operation-not-allowed':
            setError("Email/password accounts are not enabled. Please contact support.");
            break;
          default:
            setError(`Registration failed: ${err.message || "Please try again."}`);
            break;
        }
      } else {
        setError(err.message || 'An unexpected error occurred during registration. Please try again.');
      }
      console.error("Sign up error:", err);
    }
    setIsSigningUp(false);
  };

  const isLoading = authLoading || isSigningUp;

  if (user) {
     return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="ml-4">Redirecting to dashboard...</p>
      </div>
    );
  }
  
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-slate-100 p-4 selection:bg-primary/20">
      <Card className="w-full max-w-md shadow-xl rounded-xl overflow-hidden">
        <CardHeader className="bg-slate-50 p-6 text-center">
          <div className="mx-auto mb-4 flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full">
            <Youtube className="h-8 w-8 text-primary" />
          </div>
          <CardTitle className="text-2xl font-bold tracking-tight text-slate-800">Create Your Account</CardTitle>
          <CardDescription className="text-sm text-slate-500">
            Join ImpulsaTube Pro and start boosting your views!
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          {error && (
            <div className="bg-red-50 border-2 border-red-300 text-red-800 p-4 rounded-lg flex items-start text-sm shadow-sm animate-in slide-in-from-top-2 duration-300">
              <AlertTriangle className="h-5 w-5 mr-3 flex-shrink-0 mt-0.5 text-red-600" />
              <div>
                <p className="font-medium">{error}</p>
              </div>
            </div>
          )}
          {successMessage && (
             <div className="bg-green-50 border-2 border-green-300 text-green-800 p-4 rounded-lg flex items-start text-sm shadow-sm animate-in slide-in-from-top-2 duration-300">
              <ShieldCheck className="h-5 w-5 mr-3 flex-shrink-0 mt-0.5 text-green-600" />
              <div>
                <p className="font-medium">{successMessage}</p>
              </div>
            </div>
          )}
          {/* Only show form if no user and no success message (error can coexist with form) */}
          {!user && !successMessage && (
            <form onSubmit={handleSignUp} className="space-y-4">
              <div>
                <Label htmlFor="displayName" className="text-sm font-medium text-slate-700">Display Name (Optional)</Label>
                <Input
                  id="displayName"
                  type="text"
                  placeholder="Your display name"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  className="mt-1 w-full rounded-md border-slate-300 shadow-sm focus:border-primary focus:ring focus:ring-primary/50"
                />
              </div>
              <div>
                <Label htmlFor="email" className="text-sm font-medium text-slate-700">Email Address</Label>
                <Input 
                  id="email" 
                  type="email" 
                  placeholder="<EMAIL>" 
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required 
                  className="mt-1 w-full rounded-md border-slate-300 shadow-sm focus:border-primary focus:ring focus:ring-primary/50"
                />
              </div>
              <div>
                <Label htmlFor="password" className="text-sm font-medium text-slate-700">Password</Label>
                <Input 
                  id="password" 
                  type="password" 
                  placeholder="Minimum 6 characters"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required 
                  className="mt-1 w-full rounded-md border-slate-300 shadow-sm focus:border-primary focus:ring focus:ring-primary/50"
                />
              </div>
              <div>
                <Label htmlFor="confirm-password" className="text-sm font-medium text-slate-700">Confirm Password</Label>
                <Input 
                  id="confirm-password" 
                  type="password" 
                  placeholder="Re-enter your password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required 
                  className="mt-1 w-full rounded-md border-slate-300 shadow-sm focus:border-primary focus:ring focus:ring-primary/50"
                />
              </div>
              <Button type="submit" className="w-full bg-primary hover:bg-primary/90 text-white py-2.5 rounded-md font-semibold" disabled={isLoading}>
                {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Create Account'}
              </Button>
            </form>
          )}
        </CardContent>
        <CardFooter className="bg-slate-50 p-6 text-center">
          <p className="text-sm text-slate-500">
            Already have an account?{' '}
            <Link href="/login" legacyBehavior>
              <a className="font-semibold text-primary hover:text-primary/90">Log In</a>
            </Link>
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
