
"use client";

import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
// import { FcGoogle } from "react-icons/fc"; // Removed
// import { FaFacebook } from "react-icons/fa"; // Removed
import { Loader2, Youtube, AlertTriangle, ShieldCheck, MailQuestion } from "lucide-react";
import Link from 'next/link';
import { FirebaseError } from 'firebase/app';

export default function LoginPage() {
  const {
    user,
    signInWithEmailAndPassword,
    sendPasswordReset,
    // signInWithGoogle, // Removed
    // signInWithFacebook, // Removed
    loading: authLoading,
  } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isEmailLoginLoading, setIsEmailLoginLoading] = useState(false);

  const [showForgotPasswordForm, setShowForgotPasswordForm] = useState(false);
  const [forgotPasswordEmail, setForgotPasswordEmail] = useState('');
  const [forgotPasswordError, setForgotPasswordError] = useState<string | null>(null);
  const [forgotPasswordSuccess, setForgotPasswordSuccess] = useState<string | null>(null);
  const [isSendingResetEmail, setIsSendingResetEmail] = useState(false);

  useEffect(() => {
    if (user) {
      router.replace("/dashboard");
    }
  }, [user, router]);

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setForgotPasswordError(null);
    setForgotPasswordSuccess(null);
    setIsEmailLoginLoading(true);

    // Debug logging
    console.log("Login attempt:", {
      email,
      passwordLength: password.length,
      firebaseConfig: {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY?.substring(0, 10) + "...",
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID
      }
    });

    try {
      if (!signInWithEmailAndPassword) {
          throw new Error("Sign in function not available from AuthContext.");
      }
      await signInWithEmailAndPassword(email, password);
    } catch (err: any) {
      if (err instanceof FirebaseError) {
        console.error("Firebase error details:", {
          code: err.code,
          message: err.message,
          customData: err.customData
        });

        switch (err.code) {
          case 'auth/user-not-found':
          case 'auth/wrong-password':
          case 'auth/invalid-credential':
            setError("Invalid email or password. Please try again.");
            break;
          case 'auth/invalid-email':
            setError("The email address is not valid.");
            break;
          case 'auth/too-many-requests':
            setError("Access to this account has been temporarily disabled due to many failed login attempts. You can immediately restore it by resetting your password or you can try again later.");
            break;
          default:
            setError(err.message || "Failed to log in. Please try again.");
            break;
        }
      } else {
        setError(err.message || "An unexpected error occurred during login.");
      }
      console.error("Login error:", err);
    }
    setIsEmailLoginLoading(false);
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setForgotPasswordError(null);
    setForgotPasswordSuccess(null);
    setIsSendingResetEmail(true);
    try {
      if (!sendPasswordReset) {
        throw new Error("Password reset function not available from AuthContext.");
      }
      await sendPasswordReset(forgotPasswordEmail);
      setForgotPasswordSuccess("Password reset email sent! Check your inbox (and spam folder).");
      setShowForgotPasswordForm(false); 
      setForgotPasswordEmail(''); 
    } catch (err: any) {
      if (err instanceof FirebaseError) {
        switch(err.code) {
            case 'auth/user-not-found':
                 setForgotPasswordError("No account found with this email address.");
                 break;
            case 'auth/invalid-email':
                 setForgotPasswordError("The email address is not valid.");
                 break;
            default:
                setForgotPasswordError(err.message || "Failed to send password reset email. Please try again.");
        }
      } else {
        setForgotPasswordError(err.message || "An unexpected error occurred.");
      }
      console.error("Forgot password error:", err);
    }
    setIsSendingResetEmail(false);
  };

  const isLoading = authLoading || isEmailLoginLoading || isSendingResetEmail;

  // Show loader if auth context is loading, and it's not an auth page trying to show its initial form
  if (authLoading && !user && !pathname.startsWith("/(auth)")) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  if (user) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="ml-4">Redirecting to dashboard...</p>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-slate-100 p-4 selection:bg-primary/20">
      <Card className="w-full max-w-md shadow-xl rounded-xl overflow-hidden">
        <CardHeader className="bg-slate-50 p-6 text-center">
          <div className="mx-auto mb-4 flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full">
            <Youtube className="h-8 w-8 text-primary" />
          </div>
          <CardTitle className="text-2xl font-bold tracking-tight text-slate-800">Welcome Back!</CardTitle>
          <CardDescription className="text-sm text-slate-500">
            Log in to continue to ImpulsaTube Pro.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md flex items-center text-sm">
              <AlertTriangle className="h-4 w-4 mr-2 flex-shrink-0" />
              <p>{error}</p>
            </div>
          )}
          {forgotPasswordSuccess && (
            <div className="bg-green-50 border border-green-200 text-green-700 p-3 rounded-md flex items-center text-sm">
              <ShieldCheck className="h-4 w-4 mr-2 flex-shrink-0" />
              <p>{forgotPasswordSuccess}</p>
            </div>
          )}
          {forgotPasswordError && (
            <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md flex items-center text-sm">
              <AlertTriangle className="h-4 w-4 mr-2 flex-shrink-0" />
              <p>{forgotPasswordError}</p>
            </div>
          )}

          {!showForgotPasswordForm ? (
            <form onSubmit={handleEmailLogin} className="space-y-4">
              <div>
                <Label htmlFor="email" className="text-sm font-medium text-slate-700">Email Address</Label>
                <Input 
                  id="email" 
                  type="email" 
                  placeholder="<EMAIL>" 
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required 
                  className="mt-1 w-full rounded-md border-slate-300 shadow-sm focus:border-primary focus:ring focus:ring-primary/50"
                />
              </div>
              <div>
                <div className="flex justify-between items-center">
                    <Label htmlFor="password" className="text-sm font-medium text-slate-700">Password</Label>
                    <Button 
                        type="button" 
                        variant="link" 
                        onClick={() => { setShowForgotPasswordForm(true); setError(null); setForgotPasswordSuccess(null); setForgotPasswordError(null); }}
                        className="p-0 h-auto text-xs text-primary hover:text-primary/80"
                    >
                        Forgot Password?
                    </Button>
                </div>
                <Input 
                  id="password" 
                  type="password" 
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required 
                  className="mt-1 w-full rounded-md border-slate-300 shadow-sm focus:border-primary focus:ring focus:ring-primary/50"
                />
              </div>
              <Button type="submit" className="w-full bg-primary hover:bg-primary/90 text-white py-2.5 rounded-md font-semibold" disabled={isEmailLoginLoading || authLoading}>
                {isEmailLoginLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Log In'}
              </Button>
            </form>
          ) : (
            <form onSubmit={handleForgotPassword} className="space-y-4">
              <div className="flex items-center gap-2 text-sm text-slate-600 mb-2">
                <MailQuestion className="h-5 w-5 text-primary" />
                <p>Enter your email to receive a password reset link.</p>
              </div>
              <div>
                <Label htmlFor="forgot-password-email" className="text-sm font-medium text-slate-700">Email Address</Label>
                <Input 
                  id="forgot-password-email" 
                  type="email" 
                  placeholder="<EMAIL>" 
                  value={forgotPasswordEmail}
                  onChange={(e) => setForgotPasswordEmail(e.target.value)}
                  required 
                  className="mt-1 w-full rounded-md border-slate-300 shadow-sm focus:border-primary focus:ring focus:ring-primary/50"
                />
              </div>
              <Button type="submit" className="w-full bg-primary hover:bg-primary/90 text-white py-2.5 rounded-md font-semibold" disabled={isSendingResetEmail || authLoading}>
                {isSendingResetEmail ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Send Reset Link'}
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => { setShowForgotPasswordForm(false); setForgotPasswordError(null); setForgotPasswordSuccess(null); }}
                className="w-full"
                disabled={isSendingResetEmail}
              >
                Back to Log In
              </Button>
            </form>
          )}

          {/* Social Login Section Removed 
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-slate-300" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-2 text-slate-500">Or continue with</span>
            </div>
          </div>

          <div className="space-y-3">
            <Button
              onClick={signInWithGoogle} // This function is no longer available
              className="w-full py-2.5 rounded-md border border-slate-300 bg-white hover:bg-slate-50 text-slate-700 font-medium shadow-sm"
              variant="outline"
              disabled={isLoading}
            >
              <FcGoogle className="mr-2 h-5 w-5" /> 
              Sign in with Google
            </Button>
            <Button
              onClick={signInWithFacebook} // This function is no longer available
              className="w-full py-2.5 rounded-md bg-[#1877F2] hover:bg-[#166CDD] text-white font-medium shadow-sm"
              disabled={isLoading}
            >
              <FaFacebook className="mr-2 h-5 w-5" />
              Sign in with Facebook
            </Button>
          </div>
          */}
        </CardContent>
        <CardFooter className="bg-slate-50 p-6 text-center">
          <p className="text-sm text-slate-500">
            Don't have an account?{' '}
            <Link href="/signup" legacyBehavior>
                <a className="font-semibold text-primary hover:text-primary/90">Sign Up</a>
            </Link>
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
