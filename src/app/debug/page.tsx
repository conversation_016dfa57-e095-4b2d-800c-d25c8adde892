"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { testFirebaseConnection, testLogin, createTestUser } from '@/debug/firebase-test';

export default function DebugPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [results, setResults] = useState<any[]>([]);

  const addResult = (result: any) => {
    setResults(prev => [...prev, { ...result, timestamp: new Date().toISOString() }]);
  };

  const handleTestConnection = async () => {
    const result = await testFirebaseConnection();
    addResult({ type: 'connection', ...result });
  };

  const handleTestLogin = async () => {
    if (!email || !password) {
      addResult({ type: 'login', success: false, error: 'Email and password required' });
      return;
    }
    const result = await testLogin(email, password);
    addResult({ type: 'login', ...result });
  };

  const handleCreateTestUser = async () => {
    if (!email || !password) {
      addResult({ type: 'create', success: false, error: 'Email and password required' });
      return;
    }
    const result = await createTestUser(email, password);
    addResult({ type: 'create', ...result });
  };

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Firebase Debug Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Test Controls</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Email</label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Password</label>
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="password"
              />
            </div>
            <div className="space-y-2">
              <Button onClick={handleTestConnection} className="w-full">
                Test Firebase Connection
              </Button>
              <Button onClick={handleTestLogin} className="w-full">
                Test Login
              </Button>
              <Button onClick={handleCreateTestUser} className="w-full" variant="outline">
                Create Test User
              </Button>
              <Button onClick={() => setResults([])} variant="destructive" className="w-full">
                Clear Results
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {results.length === 0 ? (
                <p className="text-gray-500">No results yet</p>
              ) : (
                results.map((result, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded border text-sm ${
                      result.success === false
                        ? 'bg-red-50 border-red-200'
                        : result.success === true
                        ? 'bg-green-50 border-green-200'
                        : 'bg-blue-50 border-blue-200'
                    }`}
                  >
                    <div className="font-medium">
                      {result.type.toUpperCase()} - {new Date(result.timestamp).toLocaleTimeString()}
                    </div>
                    <pre className="mt-1 whitespace-pre-wrap text-xs">
                      {JSON.stringify(result, null, 2)}
                    </pre>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
