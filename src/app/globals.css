@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 208 100% 97%; /* Soft, desaturated light blue (#F0F8FF) */
    --foreground: 215 28% 17%; /* Dark slate blue for text */

    --card: 208 100% 98%;
    --card-foreground: 215 28% 17%;

    --popover: 208 100% 98%;
    --popover-foreground: 215 28% 17%;

    --primary: 206 95% 71%; /* Vibrant sky blue (#73C2FB) */
    --primary-foreground: 210 40% 5%; /* Darker for better contrast on primary */

    --secondary: 261 50% 85%; /* Lighter lavender as secondary */
    --secondary-foreground: 260 40% 15%;

    --muted: 208 50% 94%;
    --muted-foreground: 215 20% 45%;

    --accent: 261 44% 73%; /* Calm lavender (#B19CD9) */
    --accent-foreground: 210 40% 5%; /* Darker for better contrast on accent */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 208 30% 88%;
    --input: 208 30% 92%;
    --ring: 206 95% 71%; /* Ring with primary color */

    --radius: 0.5rem;

    /* Sidebar specific colors */
    --sidebar-background: 210 30% 96%; /* Light bluish gray */
    --sidebar-foreground: 215 28% 25%;
    --sidebar-primary: 206 95% 71%; /* Main primary for active items */
    --sidebar-primary-foreground: 210 40% 5%;
    --sidebar-accent: 208 60% 90%; /* Hover */
    --sidebar-accent-foreground: 215 28% 17%;
    --sidebar-border: 208 30% 85%;
    --sidebar-ring: 206 95% 71%;

    --chart-1: 206 95% 71%; /* Primary */
    --chart-2: 261 44% 73%; /* Accent */
    --chart-3: 197 37% 50%; /* A Teal */
    --chart-4: 43 74% 66%; /* An Orange */
    --chart-5: 27 87% 67%; /* A Salmon */
  }

  .dark {
    --background: 210 20% 12%;
    --foreground: 210 40% 95%;

    --card: 210 20% 15%;
    --card-foreground: 210 40% 95%;

    --popover: 210 20% 15%;
    --popover-foreground: 210 40% 95%;

    --primary: 206 85% 65%;
    --primary-foreground: 210 40% 98%;

    --secondary: 261 45% 55%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 15% 22%;
    --muted-foreground: 210 30% 60%;

    --accent: 261 40% 65%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 210 15% 25%;
    --input: 210 15% 28%;
    --ring: 206 85% 65%;

    /* Dark Sidebar specific colors */
    --sidebar-background: 210 20% 10%;
    --sidebar-foreground: 210 40% 90%;
    --sidebar-primary: 206 85% 65%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 15% 20%;
    --sidebar-accent-foreground: 210 40% 95%;
    --sidebar-border: 210 15% 20%;
    --sidebar-ring: 206 85% 65%;

    --chart-1: 206 85% 65%;
    --chart-2: 261 40% 65%;
    --chart-3: 197 40% 50%;
    --chart-4: 43 70% 60%;
    --chart-5: 27 80% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-geist-sans), sans-serif;
  }
}
