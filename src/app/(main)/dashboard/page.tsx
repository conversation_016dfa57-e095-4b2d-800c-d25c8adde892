"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  LogOut,
  Play,
  TrendingUp,
  Coins,
  Upload,
  Gift,
  History,
  CreditCard,
  ShoppingBag,
  Users,
  User,
  Eye,
  MessageCircle,
  Heart,
} from "lucide-react";
import { StatsCards } from "@/components/dashboard/stats-cards";
import Link from "next/link";

export default function DashboardPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    if (!loading && !user) {
      router.push("/login");
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-4">
              <Avatar className="h-10 w-10 sm:h-12 sm:w-12">
                <AvatarImage src={user.photoURL || "/placeholder.svg?height=100&width=100"} alt={user.displayName || "User"} />
                <AvatarFallback>{(user.displayName || user.email || "U").charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-lg sm:text-2xl font-bold">Welcome back, {user.displayName || user.email?.split('@')[0] || 'User'}</h1>
                <p className="text-sm text-muted-foreground">Ready to earn and promote with Tubecoins?</p>
              </div>
            </div>
            <div className="flex items-center gap-2 sm:gap-4 w-full sm:w-auto">
              <Card className="px-3 py-2 flex-1 sm:flex-none">
                <div className="flex items-center gap-2">
                  <Coins className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-500" />
                  <span className="font-bold text-base sm:text-lg">{user.tubeCoins || 0}</span>
                  <span className="text-xs sm:text-sm text-muted-foreground">Tubecoins</span>
                </div>
              </Card>
              <Button variant="outline" size="sm" asChild>
                <Link href="/profile">
                  <Avatar className="h-6 w-6 mr-2">
                    <AvatarImage src={user.photoURL || "/placeholder.svg?height=100&width=100"} alt={user.displayName || "User"} />
                    <AvatarFallback>{(user.displayName || user.email || "U").charAt(0)}</AvatarFallback>
                  </Avatar>
                  <span className="hidden sm:inline">Profile</span>
                </Link>
              </Button>
              <Button variant="outline" size="sm" onClick={() => router.push('/')}>
                <LogOut className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">Sign Out</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8 mb-8">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              <span className="hidden sm:inline">Overview</span>
            </TabsTrigger>
            <TabsTrigger value="watch" className="flex items-center gap-2">
              <Play className="h-4 w-4" />
              <span className="hidden sm:inline">Watch</span>
            </TabsTrigger>
            <TabsTrigger value="promote" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              <span className="hidden sm:inline">Promote</span>
            </TabsTrigger>
            <TabsTrigger value="offers" className="flex items-center gap-2">
              <Gift className="h-4 w-4" />
              <span className="hidden sm:inline">Offers</span>
            </TabsTrigger>
            <TabsTrigger value="buy" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              <span className="hidden sm:inline">Buy</span>
            </TabsTrigger>
            <TabsTrigger value="rewards" className="flex items-center gap-2">
              <ShoppingBag className="h-4 w-4" />
              <span className="hidden sm:inline">Rewards</span>
            </TabsTrigger>
            <TabsTrigger value="referrals" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span className="hidden sm:inline">Referrals</span>
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              <span className="hidden sm:inline">History</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <StatsCards />
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Play className="h-5 w-5" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full" onClick={() => setActiveTab("watch")}>
                    <Play className="h-4 w-4 mr-2" />
                    Watch Videos
                  </Button>
                  <Button variant="outline" className="w-full" onClick={() => setActiveTab("promote")}>
                    <Upload className="h-4 w-4 mr-2" />
                    Promote Video
                  </Button>
                  <Button variant="outline" className="w-full" onClick={() => setActiveTab("offers")}>
                    <Gift className="h-4 w-4 mr-2" />
                    Complete Offers
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm">Watched "Learn React" - +10 Tubecoins</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                      <span className="text-sm">Promoted "My Travel Vlog" - 50 views</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="h-2 w-2 bg-purple-500 rounded-full"></div>
                      <span className="text-sm">Completed survey - +25 Tubecoins</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Achievements</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Badge variant="secondary">🎯</Badge>
                      <span className="text-sm">First Video Watched</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Badge variant="secondary">💰</Badge>
                      <span className="text-sm">100 Tubecoins Earned</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Badge variant="outline">🚀</Badge>
                      <span className="text-sm text-muted-foreground">Video Promoter (Locked)</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="watch">
            <Card>
              <CardHeader>
                <CardTitle>Watch Videos Section</CardTitle>
                <CardDescription>This section will be implemented with the WatchVideosSection component</CardDescription>
              </CardHeader>
            </Card>
          </TabsContent>

          <TabsContent value="promote">
            <Card>
              <CardHeader>
                <CardTitle>Promote Videos Section</CardTitle>
                <CardDescription>This section will be implemented with the PromoteVideosSection component</CardDescription>
              </CardHeader>
            </Card>
          </TabsContent>

          <TabsContent value="offers">
            <Card>
              <CardHeader>
                <CardTitle>Offerwall Section</CardTitle>
                <CardDescription>This section will be implemented with the OfferwallSection component</CardDescription>
              </CardHeader>
            </Card>
          </TabsContent>

          <TabsContent value="buy">
            <Card>
              <CardHeader>
                <CardTitle>Buy Tubecoins Section</CardTitle>
                <CardDescription>This section will be implemented with the BuyTubecoinsSection component</CardDescription>
              </CardHeader>
            </Card>
          </TabsContent>

          <TabsContent value="rewards">
            <Card>
              <CardHeader>
                <CardTitle>Rewards Section</CardTitle>
                <CardDescription>This section will be implemented with the RewardsSection component</CardDescription>
              </CardHeader>
            </Card>
          </TabsContent>

          <TabsContent value="referrals">
            <Card>
              <CardHeader>
                <CardTitle>Referral System</CardTitle>
                <CardDescription>This section will be implemented with the ReferralSystem component</CardDescription>
              </CardHeader>
            </Card>
          </TabsContent>

          <TabsContent value="history">
            <Card>
              <CardHeader>
                <CardTitle>Transaction History</CardTitle>
                <CardDescription>This section will be implemented with the TransactionHistory component</CardDescription>
              </CardHeader>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
