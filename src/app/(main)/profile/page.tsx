
"use client";

import { useAuth } from "@/contexts/AuthContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Coins, Edit3, Film, ListChecks, Mail, User as UserIcon } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import type { Campaign, Video } from "@/types"; // Assuming Video type is defined
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";

// Mock data for videos and campaigns - replace with actual Firestore fetches
const mockUserVideos: Video[] = [
  { id: "vid1", userId: "testUser", youtubeUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ", title: "My First Vlog", description: "A day in my life.", tags: ["vlog", "daily"] },
  { id: "vid2", userId: "testUser", youtubeUrl: "https://www.youtube.com/watch?v=ysz5S6PUM-U", title: "Gaming Highlights", description: "Epic moments from my stream.", tags: ["gaming", "stream"] },
];

const mockUserCampaigns: Campaign[] = [
    { id: "camp1", creatorId: "testUser", videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ", title: "Promote My Vlog", coinsPerView: 10, totalBudget: 1000, remainingBudget: 200, views: 80, isActive: true, createdAt: new Date(), thumbnailUrl: "https://placehold.co/300x180.png?text=My+Vlog+Promo" },
    { id: "camp2", creatorId: "testUser", videoUrl: "https://www.youtube.com/watch?v=ysz5S6PUM-U", title: "Boost Gaming Stream", coinsPerView: 5, totalBudget: 500, remainingBudget: 0, views: 100, isActive: false, createdAt: new Date(), thumbnailUrl: "https://placehold.co/300x180.png?text=Gaming+Promo" },
];


export default function ProfilePage() {
  const { user, tubeCoins, signOut, fetchTubeCoins } = useAuth();
  const router = useRouter();
  const [userVideos, setUserVideos] = useState<Video[]>([]);
  const [userCampaigns, setUserCampaigns] = useState<Campaign[]>([]);

  useEffect(() => {
    // Simulate fetching user-specific data
    if (user) {
      setUserVideos(mockUserVideos.filter(v => v.userId === "testUser")); // In real app, use user.uid
      setUserCampaigns(mockUserCampaigns.filter(c => c.creatorId === "testUser")); // In real app, use user.uid
      fetchTubeCoins(); // Refresh coin balance
    }
  }, [user, fetchTubeCoins]);

  if (!user) {
    return <p>Loading profile...</p>; // Or redirect to login
  }
  
  const getInitials = (name?: string | null) => {
    if (!name) return "U";
    const names = name.split(' ');
    if (names.length === 1) return names[0].charAt(0).toUpperCase();
    return names[0].charAt(0).toUpperCase() + names[names.length - 1].charAt(0).toUpperCase();
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <Card className="w-full shadow-xl">
        <CardHeader className="bg-muted/30 p-6 rounded-t-lg">
          <div className="flex flex-col md:flex-row items-center gap-6">
            <Avatar className="h-24 w-24 border-4 border-primary shadow-md">
              <AvatarImage src={user.photoURL || undefined} alt={user.displayName || "User"} />
              <AvatarFallback className="text-3xl bg-primary text-primary-foreground">
                {getInitials(user.displayName)}
              </AvatarFallback>
            </Avatar>
            <div className="text-center md:text-left">
              <CardTitle className="text-3xl font-bold">{user.displayName || "User Name"}</CardTitle>
              <CardDescription className="text-md text-muted-foreground flex items-center justify-center md:justify-start gap-2 mt-1">
                <Mail className="h-4 w-4"/> {user.email || "No email provided"}
              </CardDescription>
              <div className="mt-3 flex items-center justify-center md:justify-start gap-2 text-lg font-semibold text-yellow-600">
                <Coins className="h-6 w-6 text-yellow-500" />
                <span>{tubeCoins?.toLocaleString() || 0} TubeCoins</span>
              </div>
            </div>
            <Button variant="outline" className="md:ml-auto mt-4 md:mt-0">
              <Edit3 className="mr-2 h-4 w-4" /> Edit Profile
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <Tabs defaultValue="campaigns" className="w-full">
            <TabsList className="grid w-full grid-cols-2 md:grid-cols-2 mb-6">
              <TabsTrigger value="campaigns" className="py-2.5">
                <ListChecks className="mr-2 h-5 w-5" /> My Campaigns
              </TabsTrigger>
              <TabsTrigger value="videos" className="py-2.5">
                <Film className="mr-2 h-5 w-5" /> My Videos
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="campaigns">
              <h3 className="text-xl font-semibold mb-4">Active & Past Campaigns</h3>
              {userCampaigns.length > 0 ? (
                <div className="space-y-4">
                  {userCampaigns.map(campaign => (
                    <Card key={campaign.id} className="flex flex-col sm:flex-row items-start gap-4 p-4 shadow-sm hover:shadow-md transition-shadow">
                      <Image
                        src={campaign.thumbnailUrl || "https://placehold.co/120x70.png?text=Campaign"}
                        alt={campaign.title}
                        width={120}
                        height={70}
                        className="rounded-md object-cover aspect-video"
                        data-ai-hint="video multimedia"
                      />
                      <div className="flex-1">
                        <h4 className="font-semibold text-lg">{campaign.title}</h4>
                        <p className="text-sm text-muted-foreground">
                          Status: {campaign.isActive && campaign.remainingBudget > 0 ? <span className="text-green-500">Active</span> : <span className="text-red-500">Inactive/Ended</span>}
                        </p>
                        <p className="text-sm">Budget: {campaign.totalBudget} TC | Remaining: {campaign.remainingBudget} TC</p>
                        <p className="text-sm">Views: {campaign.views}</p>
                      </div>
                      <Button variant="outline" size="sm" className="mt-2 sm:mt-0">Manage</Button>
                    </Card>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-6">You haven&apos;t created any campaigns yet.</p>
              )}
            </TabsContent>

            <TabsContent value="videos">
              <h3 className="text-xl font-semibold mb-4">Your Uploaded Videos</h3>
              {userVideos.length > 0 ? (
                <div className="space-y-4">
                  {userVideos.map(video => (
                     <Card key={video.id} className="flex items-center gap-4 p-4 shadow-sm hover:shadow-md transition-shadow">
                       <Image 
                         src={`https://placehold.co/100x60.png?text=${encodeURIComponent(video.title)}`} 
                         alt={video.title} 
                         width={100} 
                         height={60} 
                         className="rounded-md object-cover"
                         data-ai-hint="video play"
                       />
                       <div className="flex-1">
                         <h4 className="font-semibold text-md">{video.title}</h4>
                         <p className="text-xs text-muted-foreground line-clamp-1">{video.description}</p>
                       </div>
                       <Button variant="outline" size="sm">Details</Button>
                     </Card>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-6">You haven&apos;t added any videos yet.</p>
              )}
            </TabsContent>
          </Tabs>

          <Separator className="my-8" />
          <div className="text-center">
             <Button variant="destructive" onClick={async () => {
               await signOut();
               router.push('/');
             }}>Log Out</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
