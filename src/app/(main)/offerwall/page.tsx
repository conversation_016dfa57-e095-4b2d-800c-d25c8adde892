
"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import type { Offer } from "@/types";
import { Coins, Gift, CheckCircle2, ExternalLink, Loader2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";

// Mock data for offers
const mockOffers: Offer[] = [
  {
    id: "offer1",
    title: "Survey Master Pro",
    description: "Complete a short survey and earn TubeCoins. Share your opinions on new tech products.",
    rewardAmount: 50,
    provider: "SurveyJunkie",
    imageUrl: "https://placehold.co/600x300.png?text=Survey+Offer",
    actionUrl: "#", // Placeholder URL
  },
  {
    id: "offer2",
    title: "Game Download: Galaxy Warriors",
    description: "Download and reach level 5 in Galaxy Warriors to get your reward. Fun and addictive space shooter!",
    rewardAmount: 150,
    provider: "AppLovin",
    imageUrl: "https://placehold.co/600x300.png?text=Game+Download",
    actionUrl: "#",
  },
  {
    id: "offer3",
    title: "Sign up for Newsletter",
    description: "Subscribe to our partner's tech newsletter for the latest updates and earn TubeCoins.",
    rewardAmount: 25,
    provider: "TechWeekly",
    imageUrl: "https://placehold.co/600x300.png?text=Newsletter+Signup",
    actionUrl: "#",
  },
];


export default function OfferwallPage() {
  const { user, tubeCoins, setTubeCoins, fetchTubeCoins } = useAuth();
  const { toast } = useToast();
  const [offers, setOffers] = useState<Offer[]>([]);
  const [loadingOffers, setLoadingOffers] = useState(true);
  const [completingOfferId, setCompletingOfferId] = useState<string | null>(null);

  useEffect(() => {
    // Simulate fetching offers
    setLoadingOffers(true);
    setTimeout(() => {
      setOffers(mockOffers);
      setLoadingOffers(false);
    }, 1000);
  }, []);

  const handleCompleteOffer = async (offer: Offer) => {
    if (!user) {
      toast({ title: "Error", description: "You must be logged in.", variant: "destructive" });
      return;
    }
    setCompletingOfferId(offer.id);

    // Simulate API call to backend/Cloud Function to validate offer completion
    await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate validation delay

    const newTubeCoins = (tubeCoins || 0) + offer.rewardAmount;
    setTubeCoins(newTubeCoins);
    // In a real app, update Firestore:
    // await updateDoc(doc(db, "users", user.uid), { tubeCoins: newTubeCoins });
    // await addDoc(collection(db, "users", user.uid, "completedOffers"), { offerId: offer.id, rewardAmount: offer.rewardAmount, completedAt: new Date() });
    await fetchTubeCoins(); // Re-fetch from "source of truth"

    toast({
      title: "Offer Completed!",
      description: `You earned ${offer.rewardAmount} TubeCoins for completing "${offer.title}".`,
      action: <CheckCircle2 className="text-green-500" />,
    });
    setCompletingOfferId(null);
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <div className="text-center md:text-left mb-8">
        <h1 className="text-3xl font-bold tracking-tight text-foreground flex items-center justify-center md:justify-start gap-2">
          <Gift className="h-8 w-8 text-primary" /> Offerwall
        </h1>
        <p className="text-muted-foreground">Complete offers from our partners to earn more TubeCoins!</p>
      </div>

      {loadingOffers ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1,2,3].map(i => (
            <Card key={i} className="shadow-lg animate-pulse">
              <div className="aspect-[16/8] bg-muted rounded-t-lg" />
              <CardHeader>
                <div className="h-6 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 bg-muted rounded w-full mb-2"></div>
                <div className="h-4 bg-muted rounded w-2/3"></div>
              </CardContent>
              <CardFooter className="flex justify-between items-center">
                <div className="h-8 bg-muted rounded w-24"></div>
                <div className="h-10 bg-muted rounded w-36"></div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : offers.length === 0 ? (
        <p className="text-center text-muted-foreground py-10">No offers available right now. Check back later!</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {offers.map((offer) => (
            <Card key={offer.id} className="flex flex-col overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl">
              {offer.imageUrl && (
                <div className="relative aspect-[16/8]">
                  <Image
                    src={offer.imageUrl}
                    alt={offer.title}
                    layout="fill"
                    objectFit="cover"
                    data-ai-hint="offer promotion"
                    className="rounded-t-xl"
                  />
                </div>
              )}
              <CardHeader className="flex-grow">
                <CardTitle className="text-xl line-clamp-2">{offer.title}</CardTitle>
                <CardDescription className="text-xs text-muted-foreground">Provider: {offer.provider}</CardDescription>
                <p className="text-sm text-foreground mt-2 line-clamp-3">{offer.description}</p>
              </CardHeader>
              <CardFooter className="flex flex-col sm:flex-row justify-between items-center gap-2 border-t pt-4">
                <div className="flex items-center gap-1 font-semibold text-lg text-yellow-600">
                  <Coins className="h-5 w-5 text-yellow-500" />
                  <span>Earn {offer.rewardAmount}</span>
                </div>
                <Link href={offer.actionUrl} target="_blank" rel="noopener noreferrer" passHref>
                  <Button 
                    onClick={(e) => {
                      e.preventDefault(); // Prevent navigation for mock
                      handleCompleteOffer(offer);
                    }}
                    className="w-full sm:w-auto"
                    disabled={completingOfferId === offer.id}
                  >
                    {completingOfferId === offer.id ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <ExternalLink className="mr-2 h-4 w-4" />
                    )}
                    {completingOfferId === offer.id ? 'Completing...' : 'Start Offer'}
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
