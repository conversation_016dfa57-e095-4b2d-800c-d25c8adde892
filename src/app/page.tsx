
"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { motion } from 'framer-motion'; // Added Framer Motion
import { AnimatedStat } from '@/components/landing/AnimatedStat'; // Added AnimatedStat
import { 
  Zap, 
  TrendingUp, 
  Coins, 
  PlayCircle, 
  ChevronLeft, 
  ChevronRight, 
  Mail, 
  Phone, 
  MapPin,
  Users,       
  Eye,         
  BarChart2,   
  Link2, 
  DollarSign, 
  Youtube as YoutubeIcon 
} from 'lucide-react'; 
import { AppHeaderLanding } from '@/components/layout/AppHeaderLanding'; 

const slides = [
  {
    id: 1,
    headline: <>Skyrocket Your YouTube Views, <span className="text-primary">Effortlessly.</span></>,
    subHeadline: "Join ImpulsaTube Pro to connect with an active audience that boosts your videos and takes your channel to new heights.",
    buttonText: "Boost My Channel",
    icon: <PlayCircle className="mr-2 h-6 w-6" />
  },
  {
    id: 2,
    headline: <>Watch Content. Complete Offers. <span className="text-primary">Earn Tubecoins.</span></>,
    subHeadline: "Discover exciting new videos and complete simple app offers to earn valuable Tubecoins. Your time is rewarded!",
    buttonText: "Explore & Earn",
    icon: <Coins className="mr-2 h-6 w-6" />
  },
  {
    id: 3,
    headline: <>A Win-Win Ecosystem for <span className="text-primary">Creators & Viewers.</span></>,
    subHeadline: "ImpulsaTube Pro fosters a dynamic community where creators gain visibility and viewers are rewarded, creating a thriving cycle of engagement.",
    buttonText: "Join the Community",
    icon: <Zap className="mr-2 h-6 w-6" />
  },
];

export default function HomePage() {
  const router = useRouter();
  const [currentSlide, setCurrentSlide] = useState(0);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
  };

  useEffect(() => {
    const slideInterval = setInterval(() => {
      nextSlide();
    }, 7000); 
    return () => clearInterval(slideInterval); 
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentSlide]);

  // Animation variants for scroll-triggered animations
  const cardVariants = {
    hidden: (direction = -1) => ({ opacity: 0, x: direction * 100 }),
    visible: {
      opacity: 1,
      x: 0,
      transition: { 
        type: "spring",
        stiffness: 50,
        damping: 20,
        duration: 0.8 
      }
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-slate-900 text-white">
      <AppHeaderLanding /> 
      
      <section className="relative w-full h-[60vh] sm:h-[65vh] md:h-[75vh] lg:h-[calc(85vh-4rem)] flex items-center justify-center overflow-hidden pt-16">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-slate-900 to-slate-900 opacity-80"></div>
        <div className="relative container mx-auto px-4 md:px-6 text-center z-10">
          {slides.map((slide, index) => (
            <motion.div // Added motion.div for slide content animation
              key={slide.id}
              initial={{ opacity: 0 }}
              animate={{ opacity: currentSlide === index ? 1 : 0 }}
              transition={{ duration: 0.8, ease: "easeInOut" }}
              className={`absolute inset-0 flex flex-col items-center justify-center ${currentSlide !== index ? 'pointer-events-none' : ''}`}
            >
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl selection:bg-primary/30">
                {slide.headline}
              </h1>
              <p className="mt-6 max-w-3xl mx-auto text-lg text-slate-300 sm:text-xl md:text-2xl">
                {slide.subHeadline}
              </p>
              <div className="mt-10">
                <Button 
                  onClick={() => router.push('/login')} 
                  variant="default" 
                  size="lg"
                  className="bg-primary hover:bg-primary/90 text-slate-900 font-semibold py-4 px-10 rounded-lg shadow-lg text-lg transform transition-all duration-300 hover:scale-105 active:scale-95"
                >
                  {slide.icon} {slide.buttonText}
                </Button>
              </div>
            </motion.div>
          ))}
        </div>
        <button onClick={prevSlide} className="absolute left-4 md:left-8 top-1/2 -translate-y-1/2 z-20 p-2 bg-black/30 hover:bg-black/50 rounded-full text-white transition-colors" aria-label="Previous Slide"><ChevronLeft size={32} /></button>
        <button onClick={nextSlide} className="absolute right-4 md:right-8 top-1/2 -translate-y-1/2 z-20 p-2 bg-black/30 hover:bg-black/50 rounded-full text-white transition-colors" aria-label="Next Slide"><ChevronRight size={32} /></button>
        <div className="absolute bottom-8 left-1/2 -translate-x-1/2 z-20 flex space-x-2">
          {slides.map((_, index) => (
            <button key={index} onClick={() => setCurrentSlide(index)} className={`h-3 w-3 rounded-full transition-colors ${currentSlide === index ? 'bg-primary' : 'bg-slate-500 hover:bg-slate-400'}`} aria-label={`Go to slide ${index + 1}`} />
          ))}
        </div>
      </section>

      <section id="how-it-works-creators" className="w-full py-12 md:py-20 lg:py-28 bg-slate-800/40 overflow-hidden">
        <div className="container mx-auto px-4 md:px-6">
          <motion.header 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12 md:mb-16"
          >
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">Supercharge Your YouTube Channel</h2>
            <p className="text-lg md:text-xl text-slate-300 max-w-3xl mx-auto">Unlock your video's potential. ImpulsaTube Pro connects you with an active audience, driving genuine views and helping your content get the attention it deserves.</p>
          </motion.header>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[ // Array for mapping creator steps
              { icon: <Link2 className="h-10 w-10 text-primary" />, title: "1. Link Your Video Easily", description: "Simply submit your YouTube video URL. Our platform is designed for quick and easy campaign setup.", direction: -1 },
              { icon: <DollarSign className="h-10 w-10 text-primary" />, title: "2. Control Your Budget", description: "Define your Tubecoins-per-view and set a total campaign budget. Transparent and flexible promotion tailored to your needs.", direction: 1 },
              { icon: <Users className="h-10 w-10 text-primary" />, title: "3. Reach Real Viewers", description: "Your video is showcased to our community, ready to discover and engage. Gain valuable views that contribute to your channel's growth.", direction: -1 },
              { icon: <BarChart2 className="h-10 w-10 text-primary" />, title: "4. Track Your Impact", description: "Monitor your campaign's progress with clear stats on views delivered and Tubecoins utilized, all in real-time.", direction: 1 },
            ].map((step, idx) => (
              <motion.div 
                key={idx} 
                custom={step.direction} // Pass direction for cardVariants
                variants={cardVariants} 
                initial="hidden" 
                whileInView="visible" 
                viewport={{ once: true, amount: 0.3 }} 
                className="flex flex-col items-center text-center p-6 bg-slate-700/30 rounded-xl shadow-lg hover:shadow-primary/20 transition-shadow duration-300"
              >
                <div className="p-4 bg-primary/10 rounded-full mb-4">{step.icon}</div>
                <h3 className="text-xl font-semibold text-slate-100 mb-2">{step.title}</h3>
                <p className="text-slate-400 text-sm">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="w-full py-12 md:py-20 bg-slate-950">
        <div className="container mx-auto px-4 md:px-6">
          <motion.header 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-10 md:mb-14"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-3">Join Our Thriving Community</h2>
            <p className="text-slate-400 text-lg md:text-xl max-w-2xl mx-auto">See the impact we're making together, growing every day!</p>
          </motion.header>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <motion.div initial={{opacity:0, y:20}} whileInView={{opacity:1, y:0}} viewport={{once:true, amount:0.2}} transition={{delay:0.1, duration:0.5}} className="p-6 bg-slate-800/70 rounded-lg">
              <Users className="h-12 w-12 text-primary mx-auto mb-3" />
              <div className="text-4xl font-bold text-white"><AnimatedStat targetValue={10000} text="+"/></div>
              <p className="text-slate-400 mt-1">Active Users</p>
            </motion.div>
            <motion.div initial={{opacity:0, y:20}} whileInView={{opacity:1, y:0}} viewport={{once:true, amount:0.2}} transition={{delay:0.2, duration:0.5}} className="p-6 bg-slate-800/70 rounded-lg">
              <Eye className="h-12 w-12 text-primary mx-auto mb-3" />
              <div className="text-4xl font-bold text-white"><AnimatedStat targetValue={500000} text="+"/></div>
              <p className="text-slate-400 mt-1">Views Delivered</p>
            </motion.div>
            <motion.div initial={{opacity:0, y:20}} whileInView={{opacity:1, y:0}} viewport={{once:true, amount:0.2}} transition={{delay:0.3, duration:0.5}} className="p-6 bg-slate-800/70 rounded-lg">
              <Coins className="h-12 w-12 text-primary mx-auto mb-3" />
              <div className="text-4xl font-bold text-white"><AnimatedStat targetValue={2000000} text="M+" /></div> {/* For 2M+, pass 2 and text M+ or adjust AnimatedStat */}
              <p className="text-slate-400 mt-1">Tubecoins Earned</p>
            </motion.div>
            <motion.div initial={{opacity:0, y:20}} whileInView={{opacity:1, y:0}} viewport={{once:true, amount:0.2}} transition={{delay:0.4, duration:0.5}} className="p-6 bg-slate-800/70 rounded-lg">
              <BarChart2 className="h-12 w-12 text-primary mx-auto mb-3" />
              <div className="text-4xl font-bold text-white">Growing!</div>
              <p className="text-slate-400 mt-1">Daily Engagement</p>
            </motion.div>
          </div>
        </div>
      </section>

      <footer className="w-full py-10 md:py-16 border-t border-slate-700 bg-slate-800/50">
        {/* ... footer content ... */}
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="text-xl font-semibold text-slate-100 mb-3">ImpulsaTube Pro</h3>
              <p className="text-slate-400 text-sm max-w-md">
                Your premier platform for YouTube content promotion and viewer rewards. Join our community to boost your channel or earn by engaging!
              </p>
            </div>
            <div className="md:text-right">
              <h3 className="text-xl font-semibold text-slate-100 mb-3">Contact Us</h3>
              <a href="mailto:<EMAIL>" className="block text-slate-300 hover:text-primary transition-colors mb-1 text-sm md:text-base">
                <Mail className="inline h-4 w-4 mr-2 align-middle" /> <EMAIL>
              </a>
              <a href="tel:+13109337405" className="block text-slate-300 hover:text-primary transition-colors mb-1 text-sm md:text-base">
                <Phone className="inline h-4 w-4 mr-2 align-middle" /> (*************
              </a>
              <p className="text-slate-400 text-sm md:text-base">
                <MapPin className="inline h-4 w-4 mr-2 align-middle" /> Los Angeles, CA
              </p>
            </div>
          </div>
          <div className="border-t border-slate-600 pt-8 flex flex-col md:flex-row justify-between items-center text-xs text-slate-500">
            <p className="text-sm ">&copy; {new Date().getFullYear()} ImpulsaTube Pro (BeliveApp Tech). All Rights Reserved.</p>
            <div className="mt-4 md:mt-0 space-x-4">
              <a href="#" className="hover:text-slate-300 transition-colors">Privacy Policy</a>
              <a href="#" className="hover:text-slate-300 transition-colors">Terms of Service</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
