// Firebase connectivity test script
import { auth } from '@/lib/firebase/config';
import { signInWithEmailAndPassword, createUserWithEmailAndPassword } from 'firebase/auth';

export const testFirebaseConnection = async () => {
  console.log('Testing Firebase connection...');
  console.log('Auth instance:', auth);
  console.log('Auth config:', {
    apiKey: auth.config.apiKey?.substring(0, 10) + '...',
    authDomain: auth.config.authDomain,
    projectId: auth.config.projectId
  });
  
  return {
    isConnected: !!auth,
    config: auth.config
  };
};

export const testLogin = async (email: string, password: string) => {
  try {
    console.log('Testing login with:', { email, passwordLength: password.length });
    const result = await signInWithEmailAndPassword(auth, email, password);
    console.log('Login successful:', result.user.uid);
    return { success: true, user: result.user };
  } catch (error: any) {
    console.error('<PERSON><PERSON> failed:', error);
    return { success: false, error: error.code, message: error.message };
  }
};

export const createTestUser = async (email: string, password: string) => {
  try {
    console.log('Creating test user:', { email, passwordLength: password.length });
    const result = await createUserWithEmailAndPassword(auth, email, password);
    console.log('User created successfully:', result.user.uid);
    return { success: true, user: result.user };
  } catch (error: any) {
    console.error('User creation failed:', error);
    return { success: false, error: error.code, message: error.message };
  }
};
