# **App Name**: ImpulsaTube Pro

## Core Features:

- Social Login: Allow users to log in using their Google or Facebook account for simplified access, leveraging Firebase Authentication.
- User Profile Management: Enable users to create and manage their profiles, including TubeCoin balance, videos, and campaign information, stored in Cloud Firestore.
- Video Listing and Earning: Display a dynamic list of active video campaigns where users can earn TubeCoins by watching YouTube videos.
- View-Based Reward System: Implement a secure reward system where users receive TubeCoins after viewing a video for a specified duration, utilizing Cloud Functions for validation and transaction management in Firestore.
- Campaign Creation: Enable creators to redeem TubeCoins to promote their videos, configuring campaign details such as TubeCoins per view and total budget, managed via Firestore.
- AI-Powered Content Suggestions: Generate suggestions for video descriptions and tags by analyzing video content using a generative AI tool to improve discoverability.
- Offerwall Integration: Integrate Offerwall and video ads, using Cloud Functions as intermediaries to validate completions and reward users with TubeCoins.
- Leaderboard System: Display rankings for 'Influencers' and 'Viewers', calculated using Cloud Functions and stored in Firestore for fast retrieval.

## Style Guidelines:

- Primary color: Vibrant sky blue (#73C2FB) to reflect the uplifting potential of shared content.
- Background color: Soft, desaturated light blue (#F0F8FF).
- Accent color: A calm lavender (#B19CD9) to provide a visually striking contrast to the primary and background colors.
- Clean and modern fonts to provide an engaging and accessible visual design.
- Intuitive icons to improve user interaction with a focus on quick, scannable comprehension.
- Streamlined layout that gives precedence to interaction while guaranteeing simplicity on every screen.