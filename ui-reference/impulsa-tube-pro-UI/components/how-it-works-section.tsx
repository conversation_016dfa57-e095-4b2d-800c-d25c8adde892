import Image from "next/image"
import { ArrowRight } from "lucide-react"

export function HowItWorksSection() {
  return (
    <section className="container px-4 md:px-6 py-16 md:py-24">
      <div className="text-center space-y-4 mb-16">
        <h2 className="text-3xl md:text-4xl font-bold tracking-tighter">How ImpulsaTube Pro Works</h2>
        <p className="text-xl text-muted-foreground max-w-[700px] mx-auto">
          Our Tubecoin ecosystem creates a win-win for creators and viewers
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div className="space-y-8">
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary text-primary-foreground font-bold">
                1
              </div>
              <h3 className="text-xl font-bold">For Content Creators</h3>
            </div>
            <p className="text-muted-foreground pl-14">
              Purchase or earn Tubecoins through platform activities, then use them to boost your videos' visibility
              across our network of viewers.
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary text-primary-foreground font-bold">
                2
              </div>
              <h3 className="text-xl font-bold">For Viewers</h3>
            </div>
            <p className="text-muted-foreground pl-14">
              Watch promoted videos and complete offerwall activities like app downloads to earn Tubecoins, which can be
              redeemed for rewards.
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary text-primary-foreground font-bold">
                3
              </div>
              <h3 className="text-xl font-bold">The Ecosystem</h3>
            </div>
            <p className="text-muted-foreground pl-14">
              Our platform creates a circular economy where creators get more visibility, viewers earn rewards, and
              everyone benefits from increased engagement.
            </p>
          </div>
        </div>

        <div className="relative">
          <div className="aspect-square rounded-xl overflow-hidden border shadow-lg">
            <Image
              src="/placeholder.svg?height=600&width=600"
              width={600}
              height={600}
              alt="Tubecoin Ecosystem Diagram"
              className="object-cover"
            />
          </div>
          <div className="absolute -bottom-6 -right-6 bg-primary text-primary-foreground p-4 rounded-lg shadow-lg">
            <p className="font-bold text-lg">Tubecoins</p>
            <p className="text-sm">The currency that powers our ecosystem</p>
          </div>
        </div>
      </div>

      <div className="mt-16 flex justify-center">
        <div className="flex items-center text-primary hover:underline">
          <span className="font-medium">Learn more about our ecosystem</span>
          <ArrowRight className="ml-2 h-4 w-4" />
        </div>
      </div>
    </section>
  )
}
