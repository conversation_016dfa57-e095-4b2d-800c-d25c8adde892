"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Gift, Coins, Smartphone, Bitcoin, ShoppingBag } from "lucide-react"
import type { User } from "@/lib/auth-context"

interface RewardsSectionProps {
  user: User
}

const giftCardRewards = [
  {
    id: "amazon-10",
    name: "Amazon Gift Card",
    value: "$10",
    cost: 200,
    image: "/placeholder.svg?height=100&width=100",
    category: "shopping",
  },
  {
    id: "amazon-25",
    name: "Amazon Gift Card",
    value: "$25",
    cost: 500,
    image: "/placeholder.svg?height=100&width=100",
    category: "shopping",
  },
  {
    id: "netflix-15",
    name: "Netflix Gift Card",
    value: "$15",
    cost: 300,
    image: "/placeholder.svg?height=100&width=100",
    category: "entertainment",
  },
  {
    id: "spotify-10",
    name: "Spotify Premium",
    value: "1 Month",
    cost: 200,
    image: "/placeholder.svg?height=100&width=100",
    category: "entertainment",
  },
  {
    id: "steam-20",
    name: "Steam Gift Card",
    value: "$20",
    cost: 400,
    image: "/placeholder.svg?height=100&width=100",
    category: "gaming",
  },
  {
    id: "playstore-15",
    name: "Google Play Gift Card",
    value: "$15",
    cost: 300,
    image: "/placeholder.svg?height=100&width=100",
    category: "gaming",
  },
]

const cryptoRewards = [
  {
    id: "usdt-5",
    name: "USDT (Tether)",
    value: "$5",
    cost: 100,
    symbol: "USDT",
    network: "TRC20",
  },
  {
    id: "usdt-10",
    name: "USDT (Tether)",
    value: "$10",
    cost: 200,
    symbol: "USDT",
    network: "TRC20",
  },
  {
    id: "usdt-25",
    name: "USDT (Tether)",
    value: "$25",
    cost: 500,
    symbol: "USDT",
    network: "TRC20",
  },
  {
    id: "btc-10",
    name: "Bitcoin",
    value: "$10",
    cost: 200,
    symbol: "BTC",
    network: "Bitcoin",
  },
]

const mobileRecharges = [
  {
    id: "recharge-5",
    name: "Mobile Recharge",
    value: "$5",
    cost: 100,
    providers: ["Verizon", "AT&T", "T-Mobile", "Sprint"],
  },
  {
    id: "recharge-10",
    name: "Mobile Recharge",
    value: "$10",
    cost: 200,
    providers: ["Verizon", "AT&T", "T-Mobile", "Sprint"],
  },
  {
    id: "recharge-20",
    name: "Mobile Recharge",
    value: "$20",
    cost: 400,
    providers: ["Verizon", "AT&T", "T-Mobile", "Sprint"],
  },
]

export function RewardsSection({ user }: RewardsSectionProps) {
  const [selectedReward, setSelectedReward] = useState<any>(null)
  const [redeemingReward, setRedeemingReward] = useState<string | null>(null)
  const [phoneNumber, setPhoneNumber] = useState("")
  const [walletAddress, setWalletAddress] = useState("")
  const [selectedProvider, setSelectedProvider] = useState("")

  const handleRedeem = async (reward: any, type: string) => {
    if (user.tubecoins < reward.cost) {
      alert("Insufficient Tubecoins!")
      return
    }

    setRedeemingReward(reward.id)

    // Simulate redemption process
    setTimeout(() => {
      setRedeemingReward(null)
      setSelectedReward(null)
      alert(`Successfully redeemed ${reward.name}! Check your email for details.`)
    }, 3000)
  }

  const RewardCard = ({ reward, type, onClick }: any) => (
    <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => onClick(reward)}>
      <CardContent className="p-4">
        {type === "giftcard" && (
          <div className="space-y-3">
            <img src={reward.image || "/placeholder.svg"} alt={reward.name} className="w-full h-20 object-contain" />
            <div>
              <h3 className="font-semibold text-sm">{reward.name}</h3>
              <p className="text-lg font-bold text-primary">{reward.value}</p>
            </div>
          </div>
        )}

        {type === "crypto" && (
          <div className="space-y-3">
            <div className="flex items-center justify-center h-20 bg-gradient-to-br from-orange-100 to-yellow-100 rounded-lg">
              <Bitcoin className="h-8 w-8 text-orange-500" />
            </div>
            <div>
              <h3 className="font-semibold text-sm">{reward.name}</h3>
              <p className="text-lg font-bold text-primary">{reward.value}</p>
              <p className="text-xs text-muted-foreground">{reward.network}</p>
            </div>
          </div>
        )}

        {type === "mobile" && (
          <div className="space-y-3">
            <div className="flex items-center justify-center h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg">
              <Smartphone className="h-8 w-8 text-blue-500" />
            </div>
            <div>
              <h3 className="font-semibold text-sm">{reward.name}</h3>
              <p className="text-lg font-bold text-primary">{reward.value}</p>
              <p className="text-xs text-muted-foreground">All carriers</p>
            </div>
          </div>
        )}

        <div className="flex items-center justify-between mt-3 pt-3 border-t">
          <div className="flex items-center gap-1">
            <Coins className="h-4 w-4 text-yellow-500" />
            <span className="font-bold">{reward.cost}</span>
          </div>
          <Badge variant={user.tubecoins >= reward.cost ? "default" : "secondary"}>
            {user.tubecoins >= reward.cost ? "Available" : "Insufficient"}
          </Badge>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            Redeem Rewards
          </CardTitle>
          <CardDescription>
            Exchange your earned Tubecoins for gift cards, cryptocurrency, mobile recharges, and more!
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Current Balance */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Available for Redemption</p>
              <p className="text-3xl font-bold flex items-center gap-2 text-green-600">
                <Coins className="h-8 w-8 text-yellow-500" />
                {user.tubecoins} Tubecoins
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Redemption Value</p>
              <p className="text-lg font-semibold text-green-600">${(user.tubecoins * 0.05).toFixed(2)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="giftcards" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="giftcards" className="flex items-center gap-2">
            <ShoppingBag className="h-4 w-4" />
            Gift Cards
          </TabsTrigger>
          <TabsTrigger value="crypto" className="flex items-center gap-2">
            <Bitcoin className="h-4 w-4" />
            Cryptocurrency
          </TabsTrigger>
          <TabsTrigger value="mobile" className="flex items-center gap-2">
            <Smartphone className="h-4 w-4" />
            Mobile Recharge
          </TabsTrigger>
        </TabsList>

        <TabsContent value="giftcards">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {giftCardRewards.map((reward) => (
              <RewardCard
                key={reward.id}
                reward={reward}
                type="giftcard"
                onClick={(r: any) => setSelectedReward({ ...r, type: "giftcard" })}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="crypto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {cryptoRewards.map((reward) => (
              <RewardCard
                key={reward.id}
                reward={reward}
                type="crypto"
                onClick={(r: any) => setSelectedReward({ ...r, type: "crypto" })}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="mobile">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mobileRecharges.map((reward) => (
              <RewardCard
                key={reward.id}
                reward={reward}
                type="mobile"
                onClick={(r: any) => setSelectedReward({ ...r, type: "mobile" })}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Redemption Modal */}
      {selectedReward && (
        <Card className="border-primary">
          <CardHeader>
            <CardTitle>Redeem {selectedReward.name}</CardTitle>
            <CardDescription>Complete the redemption process for your selected reward</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="font-medium">{selectedReward.name}</span>
                <span className="font-bold">{selectedReward.value}</span>
              </div>
              <div className="flex justify-between items-center mt-2">
                <span className="text-sm text-muted-foreground">Cost</span>
                <span className="font-bold flex items-center gap-1">
                  <Coins className="h-4 w-4 text-yellow-500" />
                  {selectedReward.cost} Tubecoins
                </span>
              </div>
            </div>

            {selectedReward.type === "crypto" && (
              <div className="space-y-2">
                <Label htmlFor="wallet">Wallet Address ({selectedReward.symbol})</Label>
                <Input
                  id="wallet"
                  placeholder={`Enter your ${selectedReward.symbol} wallet address`}
                  value={walletAddress}
                  onChange={(e) => setWalletAddress(e.target.value)}
                />
                <p className="text-xs text-muted-foreground">Network: {selectedReward.network}</p>
              </div>
            )}

            {selectedReward.type === "mobile" && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    placeholder="Enter your phone number"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="provider">Carrier</Label>
                  <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your carrier" />
                    </SelectTrigger>
                    <SelectContent>
                      {selectedReward.providers?.map((provider: string) => (
                        <SelectItem key={provider} value={provider}>
                          {provider}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {selectedReward.type === "giftcard" && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-blue-800">
                  The gift card code will be sent to your registered email address within 24 hours.
                </p>
              </div>
            )}

            <div className="flex gap-2">
              <Button
                className="flex-1"
                onClick={() => handleRedeem(selectedReward, selectedReward.type)}
                disabled={
                  redeemingReward === selectedReward.id ||
                  user.tubecoins < selectedReward.cost ||
                  (selectedReward.type === "crypto" && !walletAddress) ||
                  (selectedReward.type === "mobile" && (!phoneNumber || !selectedProvider))
                }
              >
                {redeemingReward === selectedReward.id ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <Gift className="mr-2 h-4 w-4" />
                    Redeem Now
                  </>
                )}
              </Button>
              <Button variant="outline" onClick={() => setSelectedReward(null)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
