import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { History, TrendingUp, TrendingDown, Play, Upload, Gift, Filter } from "lucide-react"
import type { User } from "@/lib/auth-context"

interface TransactionHistoryProps {
  user: User
}

const mockTransactions = [
  {
    id: "1",
    type: "earned",
    description: "Watched 'Amazing Tech Review'",
    amount: 5,
    date: "2024-01-15T10:30:00Z",
    category: "video_watch",
  },
  {
    id: "2",
    type: "spent",
    description: "Promoted 'My Gaming Setup'",
    amount: -20,
    date: "2024-01-15T09:15:00Z",
    category: "video_promotion",
  },
  {
    id: "3",
    type: "earned",
    description: "Completed 'Download Music App'",
    amount: 75,
    date: "2024-01-14T16:45:00Z",
    category: "offer",
  },
  {
    id: "4",
    type: "earned",
    description: "Watched 'Cooking Tutorial'",
    amount: 5,
    date: "2024-01-14T14:20:00Z",
    category: "video_watch",
  },
  {
    id: "5",
    type: "spent",
    description: "Promoted 'Travel Vlog'",
    amount: -30,
    date: "2024-01-14T11:30:00Z",
    category: "video_promotion",
  },
  {
    id: "6",
    type: "earned",
    description: "Completed Shopping Survey",
    amount: 25,
    date: "2024-01-13T18:10:00Z",
    category: "offer",
  },
  {
    id: "7",
    type: "earned",
    description: "Watched 'Fitness Routine'",
    amount: 6,
    date: "2024-01-13T15:45:00Z",
    category: "video_watch",
  },
  {
    id: "8",
    type: "spent",
    description: "Promoted 'DIY Tutorial'",
    amount: -15,
    date: "2024-01-13T12:20:00Z",
    category: "video_promotion",
  },
]

export function TransactionHistory({ user }: TransactionHistoryProps) {
  const getTransactionIcon = (category: string) => {
    switch (category) {
      case "video_watch":
        return <Play className="h-4 w-4" />
      case "video_promotion":
        return <Upload className="h-4 w-4" />
      case "offer":
        return <Gift className="h-4 w-4" />
      default:
        return <History className="h-4 w-4" />
    }
  }

  const getTransactionColor = (type: string) => {
    return type === "earned" ? "text-green-600" : "text-red-600"
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  const totalEarned = mockTransactions.filter((t) => t.type === "earned").reduce((sum, t) => sum + t.amount, 0)

  const totalSpent = mockTransactions.filter((t) => t.type === "spent").reduce((sum, t) => sum + Math.abs(t.amount), 0)

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Transaction History
          </CardTitle>
          <CardDescription>View all your Tubecoin earnings and spending activity</CardDescription>
        </CardHeader>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Earned</p>
                <p className="text-2xl font-bold text-green-600">+{totalEarned}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Spent</p>
                <p className="text-2xl font-bold text-red-600">-{totalSpent}</p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Current Balance</p>
                <p className="text-2xl font-bold">{user.tubecoins}</p>
              </div>
              <div className="h-8 w-8 bg-primary/10 rounded-full flex items-center justify-center">
                <span className="text-primary font-bold">T</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Select defaultValue="all">
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Transactions</SelectItem>
                <SelectItem value="earned">Earned Only</SelectItem>
                <SelectItem value="spent">Spent Only</SelectItem>
              </SelectContent>
            </Select>
            <Select defaultValue="all">
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="video_watch">Video Watching</SelectItem>
                <SelectItem value="video_promotion">Video Promotion</SelectItem>
                <SelectItem value="offer">Offers</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Transaction List */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockTransactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center gap-4">
                  <div className="p-2 bg-muted rounded-full">{getTransactionIcon(transaction.category)}</div>
                  <div>
                    <p className="font-medium">{transaction.description}</p>
                    <p className="text-sm text-muted-foreground">{formatDate(transaction.date)}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`font-bold ${getTransactionColor(transaction.type)}`}>
                    {transaction.amount > 0 ? "+" : ""}
                    {transaction.amount} Tubecoins
                  </p>
                  <Badge variant="outline" className="text-xs">
                    {transaction.category.replace("_", " ")}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-6 text-center">
            <Button variant="outline">Load More Transactions</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
