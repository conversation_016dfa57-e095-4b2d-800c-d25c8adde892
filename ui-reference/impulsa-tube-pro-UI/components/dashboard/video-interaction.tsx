"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Heart, MessageCircle, Flag, Send, ThumbsUp, ThumbsDown } from "lucide-react"
import type { User } from "@/lib/auth-context"

interface VideoInteractionProps {
  videoId: string
  user: User
  initialLikes?: number
  initialDislikes?: number
  initialComments?: any[]
  userHasLiked?: boolean
  userHasDisliked?: boolean
}

const reportReasons = [
  "Spam or misleading content",
  "Inappropriate content",
  "Copyright violation",
  "Harassment or bullying",
  "Violence or dangerous content",
  "Hate speech",
  "Other",
]

export function VideoInteraction({
  videoId,
  user,
  initialLikes = 0,
  initialDislikes = 0,
  initialComments = [],
  userHasLiked = false,
  userHasDisliked = false,
}: VideoInteractionProps) {
  const [likes, setLikes] = useState(initialLikes)
  const [dislikes, setDislikes] = useState(initialDislikes)
  const [hasLiked, setHasLiked] = useState(userHasLiked)
  const [hasDisliked, setHasDisliked] = useState(userHasDisliked)
  const [comments, setComments] = useState(initialComments)
  const [newComment, setNewComment] = useState("")
  const [reportReason, setReportReason] = useState("")
  const [reportDescription, setReportDescription] = useState("")
  const [isReporting, setIsReporting] = useState(false)

  const handleLike = () => {
    if (hasLiked) {
      setLikes(likes - 1)
      setHasLiked(false)
    } else {
      setLikes(likes + 1)
      setHasLiked(true)
      if (hasDisliked) {
        setDislikes(dislikes - 1)
        setHasDisliked(false)
      }
    }
  }

  const handleDislike = () => {
    if (hasDisliked) {
      setDislikes(dislikes - 1)
      setHasDisliked(false)
    } else {
      setDislikes(dislikes + 1)
      setHasDisliked(true)
      if (hasLiked) {
        setLikes(likes - 1)
        setHasLiked(false)
      }
    }
  }

  const handleComment = () => {
    if (newComment.trim()) {
      const comment = {
        id: Date.now().toString(),
        user: user.name,
        avatar: user.avatarUrl,
        content: newComment,
        timestamp: new Date().toISOString(),
        likes: 0,
      }
      setComments([comment, ...comments])
      setNewComment("")
    }
  }

  const handleReport = async () => {
    setIsReporting(true)
    // Simulate report submission
    setTimeout(() => {
      setIsReporting(false)
      setReportReason("")
      setReportDescription("")
      alert("Report submitted successfully. We'll review it within 24 hours.")
    }, 2000)
  }

  return (
    <div className="space-y-4">
      {/* Like/Dislike/Report Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant={hasLiked ? "default" : "outline"}
            size="sm"
            onClick={handleLike}
            className="flex items-center gap-2"
          >
            <ThumbsUp className="h-4 w-4" />
            {likes}
          </Button>
          <Button
            variant={hasDisliked ? "default" : "outline"}
            size="sm"
            onClick={handleDislike}
            className="flex items-center gap-2"
          >
            <ThumbsDown className="h-4 w-4" />
            {dislikes}
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <MessageCircle className="h-4 w-4" />
            {comments.length}
          </Button>
        </div>

        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
              <Flag className="h-4 w-4 mr-2" />
              Report
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Report Video</DialogTitle>
              <DialogDescription>Help us keep the platform safe by reporting inappropriate content.</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Reason for reporting</label>
                <Select value={reportReason} onValueChange={setReportReason}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a reason" />
                  </SelectTrigger>
                  <SelectContent>
                    {reportReasons.map((reason) => (
                      <SelectItem key={reason} value={reason}>
                        {reason}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Additional details (optional)</label>
                <Textarea
                  placeholder="Provide more details about the issue..."
                  value={reportDescription}
                  onChange={(e) => setReportDescription(e.target.value)}
                />
              </div>
              <Button onClick={handleReport} disabled={!reportReason || isReporting} className="w-full">
                {isReporting ? "Submitting..." : "Submit Report"}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Comments Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Comments ({comments.length})</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Add Comment */}
          <div className="flex gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={user.avatarUrl || "/placeholder.svg"} alt={user.name} />
              <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="flex-1 space-y-2">
              <Textarea
                placeholder="Add a comment..."
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                className="min-h-[80px]"
              />
              <Button onClick={handleComment} disabled={!newComment.trim()} size="sm">
                <Send className="h-4 w-4 mr-2" />
                Comment
              </Button>
            </div>
          </div>

          {/* Comments List */}
          <div className="space-y-4">
            {comments.length === 0 ? (
              <p className="text-center text-muted-foreground py-8">No comments yet. Be the first to comment!</p>
            ) : (
              comments.map((comment) => (
                <div key={comment.id} className="flex gap-3 p-3 bg-muted/50 rounded-lg">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={comment.avatar || "/placeholder.svg"} alt={comment.user} />
                    <AvatarFallback>{comment.user.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">{comment.user}</span>
                      <span className="text-xs text-muted-foreground">
                        {new Date(comment.timestamp).toLocaleDateString()}
                      </span>
                    </div>
                    <p className="text-sm">{comment.content}</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                        <Heart className="h-3 w-3 mr-1" />
                        {comment.likes}
                      </Button>
                      <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                        Reply
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
