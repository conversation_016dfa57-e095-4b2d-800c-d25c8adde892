"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Play, Coins, Eye, Heart, MessageCircle } from "lucide-react"
import { VideoSearch } from "./video-search"
import { VideoInteraction } from "./video-interaction"
import type { User } from "@/lib/auth-context"

interface WatchVideosSectionProps {
  user: User
}

const mockVideos = [
  {
    id: "1",
    title: "Amazing Tech Review - iPhone 15 Pro Max",
    channel: "TechGuru",
    channelAvatar: "/placeholder.svg?height=40&width=40",
    thumbnail: "/placeholder.svg?height=180&width=320",
    duration: "12:34",
    views: "1.2M",
    reward: 5,
    category: "Technology",
    likes: 1247,
    dislikes: 23,
    comments: 89,
    description: "Complete review of the latest iPhone with all the new features and improvements.",
  },
  {
    id: "2",
    title: "Best Gaming Setup for 2024",
    channel: "GameMaster",
    channelAvatar: "/placeholder.svg?height=40&width=40",
    thumbnail: "/placeholder.svg?height=180&width=320",
    duration: "8:45",
    views: "856K",
    reward: 5,
    category: "Gaming",
    likes: 2341,
    dislikes: 45,
    comments: 156,
    description: "Everything you need to build the perfect gaming setup this year.",
  },
  {
    id: "3",
    title: "Cooking the Perfect Pasta",
    channel: "ChefLife",
    channelAvatar: "/placeholder.svg?height=40&width=40",
    thumbnail: "/placeholder.svg?height=180&width=320",
    duration: "15:22",
    views: "432K",
    reward: 7,
    category: "Cooking",
    likes: 892,
    dislikes: 12,
    comments: 67,
    description: "Learn the secrets to making restaurant-quality pasta at home.",
  },
  {
    id: "4",
    title: "Travel Vlog: Tokyo Adventures",
    channel: "Wanderlust",
    channelAvatar: "/placeholder.svg?height=40&width=40",
    thumbnail: "/placeholder.svg?height=180&width=320",
    duration: "20:15",
    views: "2.1M",
    reward: 8,
    category: "Travel",
    likes: 5432,
    dislikes: 87,
    comments: 234,
    description: "Join me on an incredible journey through Tokyo's hidden gems.",
  },
  {
    id: "5",
    title: "Fitness Routine for Beginners",
    channel: "FitLife",
    channelAvatar: "/placeholder.svg?height=40&width=40",
    thumbnail: "/placeholder.svg?height=180&width=320",
    duration: "18:30",
    views: "678K",
    reward: 6,
    category: "Fitness",
    likes: 1876,
    dislikes: 34,
    comments: 123,
    description: "Start your fitness journey with this beginner-friendly workout routine.",
  },
  {
    id: "6",
    title: "DIY Home Decoration Ideas",
    channel: "HomeDesign",
    channelAvatar: "/placeholder.svg?height=40&width=40",
    thumbnail: "/placeholder.svg?height=180&width=320",
    duration: "14:12",
    views: "543K",
    reward: 5,
    category: "Lifestyle",
    likes: 1234,
    dislikes: 28,
    comments: 89,
    description: "Transform your home with these creative and budget-friendly decoration ideas.",
  },
]

export function WatchVideosSection({ user }: WatchVideosSectionProps) {
  const [watchingVideo, setWatchingVideo] = useState<string | null>(null)
  const [selectedVideo, setSelectedVideo] = useState<any>(null)
  const [filteredVideos, setFilteredVideos] = useState(mockVideos)

  const handleSearch = (query: string, filters: any) => {
    let filtered = mockVideos

    if (query) {
      filtered = filtered.filter(
        (video) =>
          video.title.toLowerCase().includes(query.toLowerCase()) ||
          video.channel.toLowerCase().includes(query.toLowerCase()) ||
          video.category.toLowerCase().includes(query.toLowerCase()),
      )
    }

    if (filters.category) {
      filtered = filtered.filter((video) => video.category === filters.category)
    }

    if (filters.minReward) {
      filtered = filtered.filter((video) => video.reward >= filters.minReward)
    }

    if (filters.maxReward) {
      filtered = filtered.filter((video) => video.reward <= filters.maxReward)
    }

    if (filters.sortBy) {
      switch (filters.sortBy) {
        case "newest":
          // In a real app, you'd sort by upload date
          break
        case "most_viewed":
          filtered.sort(
            (a, b) => Number.parseInt(b.views.replace(/[^\d]/g, "")) - Number.parseInt(a.views.replace(/[^\d]/g, "")),
          )
          break
        case "highest_reward":
          filtered.sort((a, b) => b.reward - a.reward)
          break
        case "lowest_reward":
          filtered.sort((a, b) => a.reward - b.reward)
          break
      }
    }

    setFilteredVideos(filtered)
  }

  const handleWatchVideo = (video: any) => {
    setWatchingVideo(video.id)
    setSelectedVideo(video)
    // Simulate watching video
    setTimeout(() => {
      setWatchingVideo(null)
      // In a real app, you would update the user's Tubecoin balance here
      alert(`You earned ${video.reward} Tubecoins for watching "${video.title}"!`)
    }, 3000)
  }

  if (selectedVideo && !watchingVideo) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                {selectedVideo.title}
              </CardTitle>
              <Button variant="outline" onClick={() => setSelectedVideo(null)}>
                Back to Videos
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Video Player Placeholder */}
            <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
              <img
                src={selectedVideo.thumbnail || "/placeholder.svg"}
                alt={selectedVideo.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <Button
                  size="lg"
                  className="rounded-full w-16 h-16 p-0"
                  onClick={() => handleWatchVideo(selectedVideo)}
                >
                  <Play className="h-8 w-8" />
                </Button>
              </div>
            </div>

            {/* Video Info */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={selectedVideo.channelAvatar || "/placeholder.svg"} />
                    <AvatarFallback>{selectedVideo.channel.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-semibold">{selectedVideo.channel}</p>
                    <p className="text-sm text-muted-foreground">{selectedVideo.views} views</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{selectedVideo.category}</Badge>
                  <div className="flex items-center gap-1 text-green-600 font-bold">
                    <Coins className="h-4 w-4" />+{selectedVideo.reward}
                  </div>
                </div>
              </div>

              <p className="text-muted-foreground">{selectedVideo.description}</p>

              {/* Video Interactions */}
              <VideoInteraction
                videoId={selectedVideo.id}
                user={user}
                initialLikes={selectedVideo.likes}
                initialDislikes={selectedVideo.dislikes}
                initialComments={[]}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            Watch Videos & Earn Tubecoins
          </CardTitle>
          <CardDescription>
            Watch promoted videos to earn Tubecoins. Each video you watch completely will reward you with Tubecoins.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Search */}
      <VideoSearch onSearch={handleSearch} />

      {/* Videos Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredVideos.map((video) => (
          <Card key={video.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <div className="relative">
              <img src={video.thumbnail || "/placeholder.svg"} alt={video.title} className="w-full h-48 object-cover" />
              <div className="absolute bottom-2 right-2 bg-black/80 text-white px-2 py-1 rounded text-xs">
                {video.duration}
              </div>
              <div className="absolute top-2 left-2">
                <Badge variant="secondary">{video.category}</Badge>
              </div>
            </div>
            <CardContent className="p-4">
              <h3 className="font-semibold text-sm mb-2 line-clamp-2">{video.title}</h3>
              <div className="flex items-center gap-2 mb-3">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={video.channelAvatar || "/placeholder.svg"} />
                  <AvatarFallback>{video.channel.charAt(0)}</AvatarFallback>
                </Avatar>
                <span className="text-sm text-muted-foreground">{video.channel}</span>
              </div>
              <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    {video.views}
                  </div>
                  <div className="flex items-center gap-1">
                    <Heart className="h-3 w-3" />
                    {video.likes}
                  </div>
                  <div className="flex items-center gap-1">
                    <MessageCircle className="h-3 w-3" />
                    {video.comments}
                  </div>
                </div>
                <div className="flex items-center gap-1 text-green-600 font-medium">
                  <Coins className="h-3 w-3" />+{video.reward}
                </div>
              </div>
              <Button className="w-full" onClick={() => setSelectedVideo(video)} disabled={watchingVideo === video.id}>
                {watchingVideo === video.id ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Watching...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    Watch & Earn
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredVideos.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Play className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No videos found matching your search criteria.</p>
            <Button className="mt-4" onClick={() => handleSearch("", {})}>
              Show All Videos
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
