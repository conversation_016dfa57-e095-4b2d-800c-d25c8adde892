"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Copy, Share2, Users, Coins, Trophy } from "lucide-react"
import type { User } from "@/lib/auth-context"
import { Label } from "@/components/ui/label"

interface ReferralSystemProps {
  user: User
}

const referralTiers = [
  { level: 1, referrals: 5, bonus: 50, title: "Bronze Referrer" },
  { level: 2, referrals: 15, bonus: 150, title: "Silver Referrer" },
  { level: 3, referrals: 30, bonus: 300, title: "Gold Referrer" },
  { level: 4, referrals: 50, bonus: 500, title: "Platinum Referrer" },
  { level: 5, referrals: 100, bonus: 1000, title: "Diamond Referrer" },
]

const mockReferrals = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    joinDate: "2024-01-15",
    status: "active",
    earned: 25,
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "2",
    name: "Bob Smith",
    email: "<EMAIL>",
    joinDate: "2024-01-10",
    status: "active",
    earned: 30,
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "3",
    name: "Carol Davis",
    email: "<EMAIL>",
    joinDate: "2024-01-08",
    status: "pending",
    earned: 0,
    avatar: "/placeholder.svg?height=40&width=40",
  },
]

export function ReferralSystem({ user }: ReferralSystemProps) {
  const [referralCode] = useState(`TUBE${user.id.toUpperCase()}`)
  const [referralLink] = useState(`https://impulsatube.pro/register?ref=${referralCode}`)
  const [totalReferrals] = useState(mockReferrals.length)
  const [totalEarned] = useState(125)
  const [copied, setCopied] = useState(false)

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const shareReferral = () => {
    if (navigator.share) {
      navigator.share({
        title: "Join ImpulsaTube Pro",
        text: "Earn Tubecoins by watching videos and promoting your content!",
        url: referralLink,
      })
    } else {
      copyToClipboard(referralLink)
    }
  }

  const currentTier =
    referralTiers.find((tier) => totalReferrals < tier.referrals) || referralTiers[referralTiers.length - 1]
  const nextTier = referralTiers.find((tier) => tier.level === currentTier.level + 1)
  const progress = nextTier ? (totalReferrals / nextTier.referrals) * 100 : 100

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Referral Program
          </CardTitle>
          <CardDescription>
            Invite friends and earn 25 Tubecoins for each successful referral. Your friends get 50 bonus Tubecoins too!
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Referral Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Referrals</p>
                <p className="text-2xl sm:text-3xl font-bold">{totalReferrals}</p>
              </div>
              <Users className="h-6 w-6 sm:h-8 sm:w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Earned</p>
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{totalEarned}</p>
              </div>
              <Coins className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Current Tier</p>
                <p className="text-base sm:text-lg font-bold">{currentTier.title}</p>
              </div>
              <Trophy className="h-6 w-6 sm:h-8 sm:w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Referral Progress */}
      {nextTier && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Progress to {nextTier.title}</CardTitle>
            <CardDescription>
              Refer {nextTier.referrals - totalReferrals} more users to unlock {nextTier.bonus} bonus Tubecoins
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{totalReferrals} referrals</span>
                <span>{nextTier.referrals} needed</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Referral Link */}
      <Card>
        <CardHeader>
          <CardTitle>Your Referral Link</CardTitle>
          <CardDescription>Share this link with friends to start earning referral bonuses</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-2">
            <Input value={referralLink} readOnly className="flex-1" />
            <div className="flex gap-2">
              <Button onClick={() => copyToClipboard(referralLink)} variant="outline" className="flex-1 sm:flex-none">
                <Copy className="h-4 w-4 mr-2" />
                {copied ? "Copied!" : "Copy"}
              </Button>
              <Button onClick={shareReferral} className="flex-1 sm:flex-none">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="referralCode">Your Referral Code</Label>
            <div className="flex gap-2">
              <Input id="referralCode" value={referralCode} readOnly className="flex-1" />
              <Button onClick={() => copyToClipboard(referralCode)} variant="outline" size="sm">
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Referral Tiers */}
      <Card>
        <CardHeader>
          <CardTitle>Referral Tiers & Rewards</CardTitle>
          <CardDescription>Unlock bigger bonuses as you refer more users</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {referralTiers.map((tier) => (
              <div
                key={tier.level}
                className={`flex items-center justify-between p-4 rounded-lg border ${
                  totalReferrals >= tier.referrals ? "bg-green-50 border-green-200" : "bg-muted/50"
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${totalReferrals >= tier.referrals ? "bg-green-100" : "bg-muted"}`}>
                    <Trophy className="h-4 w-4" />
                  </div>
                  <div>
                    <p className="font-medium">{tier.title}</p>
                    <p className="text-sm text-muted-foreground">{tier.referrals} referrals required</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-green-600">+{tier.bonus} Tubecoins</p>
                  {totalReferrals >= tier.referrals && <Badge className="mt-1">Unlocked</Badge>}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Referral History */}
      <Card>
        <CardHeader>
          <CardTitle>Your Referrals</CardTitle>
          <CardDescription>Track your referred users and earnings</CardDescription>
        </CardHeader>
        <CardContent>
          {mockReferrals.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No referrals yet. Start sharing your link!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {mockReferrals.map((referral) => (
                <div key={referral.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={referral.avatar || "/placeholder.svg"} alt={referral.name} />
                      <AvatarFallback>{referral.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{referral.name}</p>
                      <p className="text-sm text-muted-foreground">
                        Joined {new Date(referral.joinDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={referral.status === "active" ? "default" : "secondary"}>{referral.status}</Badge>
                    <p className="text-sm font-medium mt-1">+{referral.earned} Tubecoins earned</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
