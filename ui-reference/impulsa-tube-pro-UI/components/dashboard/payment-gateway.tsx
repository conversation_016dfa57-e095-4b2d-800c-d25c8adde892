"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { CreditCard, Shield, Lock, CheckCircle, Info, AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface PaymentGatewayProps {
  selectedPackage: any
  onPaymentSuccess: (transactionId: string) => void
  onCancel: () => void
}

const paymentMethods = [
  {
    id: "card",
    name: "Credit/Debit Card",
    icon: "💳",
    description: "Visa, Mastercard, American Express (via Stripe)",
    processingFee: 2.9,
  },
  {
    id: "paypal",
    name: "PayPal",
    icon: "🅿️",
    description: "Pay with your PayPal account (via Stripe)",
    processingFee: 3.5,
  },
  {
    id: "crypto",
    name: "Cryptocurrency",
    icon: "₿",
    description: "Bitcoin, Ethereum, USDT (Coming Soon)",
    processingFee: 1.0,
    disabled: true,
  },
  {
    id: "applepay",
    name: "Apple Pay",
    icon: "🍎",
    description: "Quick payment with Touch ID (via Stripe)",
    processingFee: 2.9,
  },
  {
    id: "googlepay",
    name: "Google Pay",
    icon: "🔵",
    description: "Fast and secure payments (via Stripe)",
    processingFee: 2.9,
  },
]

export function PaymentGateway({ selectedPackage, onPaymentSuccess, onCancel }: PaymentGatewayProps) {
  const [selectedMethod, setSelectedMethod] = useState("")
  const [cardNumber, setCardNumber] = useState("")
  const [expiryDate, setExpiryDate] = useState("")
  const [cvv, setCvv] = useState("")
  const [cardName, setCardName] = useState("")
  const [billingEmail, setBillingEmail] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [step, setStep] = useState(1) // 1: Method selection, 2: Payment details, 3: Processing

  const selectedPaymentMethod = paymentMethods.find((method) => method.id === selectedMethod)
  const processingFee = selectedPaymentMethod ? (selectedPackage.price * selectedPaymentMethod.processingFee) / 100 : 0
  const totalAmount = selectedPackage.price + processingFee

  const handleMethodSelect = (methodId: string) => {
    const method = paymentMethods.find((m) => m.id === methodId)
    if (method && !method.disabled) {
      setSelectedMethod(methodId)
      setStep(2)
    }
  }

  const handlePayment = async () => {
    setIsProcessing(true)
    setStep(3)

    // Simulate payment processing
    setTimeout(() => {
      const transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      onPaymentSuccess(transactionId)
      setIsProcessing(false)
    }, 3000)
  }

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "")
    const matches = v.match(/\d{4,16}/g)
    const match = (matches && matches[0]) || ""
    const parts = []
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4))
    }
    if (parts.length) {
      return parts.join(" ")
    } else {
      return v
    }
  }

  if (step === 1) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Choose Payment Method</CardTitle>
          <CardDescription>Select how you'd like to pay for your Tubecoins</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Order Summary */}
          <div className="bg-muted/50 p-4 rounded-lg space-y-2">
            <div className="flex justify-between">
              <span className="font-medium">{selectedPackage.name}</span>
              <span>${selectedPackage.price}</span>
            </div>
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>{selectedPackage.tubecoins.toLocaleString()} Tubecoins</span>
              {selectedPackage.bonus > 0 && <span>+{selectedPackage.bonus} Bonus</span>}
            </div>
          </div>

          <Alert className="bg-blue-50 border-blue-200">
            <Info className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              All payments are securely processed through Stripe, our trusted payment provider.
            </AlertDescription>
          </Alert>

          {/* Payment Methods */}
          <div className="space-y-3">
            {paymentMethods.map((method) => (
              <Card
                key={method.id}
                className={`cursor-pointer hover:shadow-md transition-shadow ${method.disabled ? "opacity-60" : ""}`}
                onClick={() => !method.disabled && handleMethodSelect(method.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{method.icon}</span>
                      <div>
                        <p className="font-medium">{method.name}</p>
                        <p className="text-sm text-muted-foreground">{method.description}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      {method.disabled ? (
                        <Badge variant="outline" className="text-amber-600 border-amber-300 bg-amber-50">
                          Coming Soon
                        </Badge>
                      ) : (
                        <Badge variant="secondary">{method.processingFee}% fee</Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Button variant="outline" onClick={onCancel} className="w-full">
            Cancel
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (step === 2) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Details
          </CardTitle>
          <CardDescription>Complete your payment with {selectedPaymentMethod?.name}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Order Summary */}
          <div className="bg-muted/50 p-4 rounded-lg space-y-2">
            <div className="flex justify-between">
              <span>{selectedPackage.name}</span>
              <span>${selectedPackage.price}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Processing fee ({selectedPaymentMethod?.processingFee}%)</span>
              <span>${processingFee.toFixed(2)}</span>
            </div>
            <Separator />
            <div className="flex justify-between font-bold">
              <span>Total</span>
              <span>${totalAmount.toFixed(2)}</span>
            </div>
          </div>

          {/* Payment Form */}
          {selectedMethod === "card" && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="cardNumber">Card Number</Label>
                <Input
                  id="cardNumber"
                  placeholder="1234 5678 9012 3456"
                  value={cardNumber}
                  onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
                  maxLength={19}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expiry">Expiry Date</Label>
                  <Input
                    id="expiry"
                    placeholder="MM/YY"
                    value={expiryDate}
                    onChange={(e) => setExpiryDate(e.target.value)}
                    maxLength={5}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cvv">CVV</Label>
                  <Input
                    id="cvv"
                    placeholder="123"
                    value={cvv}
                    onChange={(e) => setCvv(e.target.value)}
                    maxLength={4}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="cardName">Cardholder Name</Label>
                <Input
                  id="cardName"
                  placeholder="John Doe"
                  value={cardName}
                  onChange={(e) => setCardName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Billing Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={billingEmail}
                  onChange={(e) => setBillingEmail(e.target.value)}
                />
              </div>
            </div>
          )}

          {selectedMethod === "paypal" && (
            <div className="text-center py-8">
              <div className="text-6xl mb-4">🅿️</div>
              <p className="text-lg font-medium">You'll be redirected to PayPal</p>
              <p className="text-muted-foreground">Complete your payment securely with PayPal via Stripe</p>
            </div>
          )}

          {selectedMethod === "crypto" && (
            <div className="space-y-4">
              <Alert variant="warning" className="bg-amber-50 border-amber-200">
                <AlertCircle className="h-4 w-4 text-amber-600" />
                <AlertDescription className="text-amber-800">
                  Cryptocurrency payments are currently under development. We're working on integrating a secure crypto
                  payment solution.
                </AlertDescription>
              </Alert>
            </div>
          )}

          {(selectedMethod === "applepay" || selectedMethod === "googlepay") && (
            <div className="text-center py-8">
              <div className="text-6xl mb-4">{selectedPaymentMethod?.icon}</div>
              <p className="text-lg font-medium">Use {selectedPaymentMethod?.name}</p>
              <p className="text-muted-foreground">Authenticate with your device to complete payment via Stripe</p>
            </div>
          )}

          {/* Security Notice */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground bg-green-50 p-3 rounded-lg">
            <Shield className="h-4 w-4 text-green-600" />
            <span>Your payment is secured with 256-bit SSL encryption through Stripe</span>
          </div>

          <div className="flex gap-2">
            <Button onClick={() => setStep(1)} variant="outline" className="flex-1">
              Back
            </Button>
            <Button onClick={handlePayment} className="flex-1" disabled={selectedMethod === "crypto"}>
              <Lock className="mr-2 h-4 w-4" />
              Complete Payment
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (step === 3) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          {isProcessing ? (
            <div className="space-y-4">
              <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-primary mx-auto"></div>
              <h3 className="text-xl font-bold">Processing Payment...</h3>
              <p className="text-muted-foreground">Please don't close this window</p>
            </div>
          ) : (
            <div className="space-y-4">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
              <h3 className="text-xl font-bold text-green-600">Payment Successful!</h3>
              <p className="text-muted-foreground">Your Tubecoins have been added to your account</p>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return null
}
