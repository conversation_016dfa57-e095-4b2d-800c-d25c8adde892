"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, SlidersHorizontal } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

interface VideoSearchProps {
  onSearch: (query: string, filters: any) => void
}

const categories = [
  "All Categories",
  "Technology",
  "Gaming",
  "Cooking",
  "Travel",
  "Fitness",
  "Lifestyle",
  "Education",
  "Entertainment",
  "Music",
  "Sports",
]

const sortOptions = [
  { value: "newest", label: "Newest First" },
  { value: "oldest", label: "Oldest First" },
  { value: "most_viewed", label: "Most Viewed" },
  { value: "highest_reward", label: "Highest Reward" },
  { value: "lowest_reward", label: "Lowest Reward" },
]

export function VideoSearch({ onSearch }: VideoSearchProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All Categories")
  const [sortBy, setSortBy] = useState("newest")
  const [showFilters, setShowFilters] = useState(false)
  const [minReward, setMinReward] = useState("")
  const [maxReward, setMaxReward] = useState("")

  const handleSearch = () => {
    const filters = {
      category: selectedCategory === "All Categories" ? "" : selectedCategory,
      sortBy,
      minReward: minReward ? Number.parseInt(minReward) : undefined,
      maxReward: maxReward ? Number.parseInt(maxReward) : undefined,
    }
    onSearch(searchQuery, filters)
  }

  const clearFilters = () => {
    setSearchQuery("")
    setSelectedCategory("All Categories")
    setSortBy("newest")
    setMinReward("")
    setMaxReward("")
    onSearch("", {})
  }

  return (
    <Card>
      <CardContent className="p-6 space-y-4">
        {/* Main Search */}
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search videos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              onKeyPress={(e) => e.key === "Enter" && handleSearch()}
            />
          </div>
          <div className="flex gap-2">
            <Button onClick={handleSearch} className="flex-1 sm:flex-none">
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
            <Button variant="outline" onClick={() => setShowFilters(!showFilters)} className="flex-1 sm:flex-none">
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </div>

        {/* Quick Category Filters */}
        <div className="flex flex-wrap gap-2">
          {categories.slice(0, 6).map((category) => (
            <Badge
              key={category}
              variant={selectedCategory === category ? "default" : "secondary"}
              className="cursor-pointer"
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Badge>
          ))}
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="space-y-2">
              <label className="text-sm font-medium">Category</label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Sort By</label>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Min Reward</label>
              <Input type="number" placeholder="5" value={minReward} onChange={(e) => setMinReward(e.target.value)} />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Max Reward</label>
              <Input type="number" placeholder="20" value={maxReward} onChange={(e) => setMaxReward(e.target.value)} />
            </div>

            <div className="sm:col-span-2 lg:col-span-4 flex flex-col sm:flex-row gap-2">
              <Button onClick={handleSearch} className="flex-1">
                Apply Filters
              </Button>
              <Button variant="outline" onClick={clearFilters} className="flex-1">
                Clear All
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
