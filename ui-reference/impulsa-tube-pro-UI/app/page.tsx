import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowR<PERSON>, Play, TrendingUp, Users, Zap, CreditCard, Gift } from "lucide-react"
import Image from "next/image"
import { HowItWorksSection } from "@/components/how-it-works-section"
import { FeatureCard } from "@/components/feature-card"
import { TestimonialCard } from "@/components/testimonial-card"
import { FAQSection } from "@/components/faq-section"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-gradient-to-b from-primary/20 to-background pt-16">
          <div className="container px-4 md:px-6 flex flex-col items-center text-center space-y-10 py-16 md:py-24">
            <div className="space-y-4 max-w-3xl">
              <h1 className="text-4xl md:text-6xl font-bold tracking-tighter">
                Boost Your YouTube Content with <span className="text-primary">ImpulsaTube Pro</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-[700px] mx-auto">
                Connect creators with viewers through our innovative Tubecoin ecosystem. Watch videos, earn rewards,
                promote content, and redeem amazing prizes!
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md mx-auto">
              <Button size="lg" className="w-full" asChild>
                <Link href="/register">
                  Get Started Free <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="w-full" asChild>
                <Link href="/login">
                  Sign In <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
            <div className="relative w-full max-w-5xl mx-auto mt-8">
              <div className="aspect-video rounded-xl overflow-hidden shadow-2xl border">
                <Image
                  src="/placeholder.svg?height=720&width=1280"
                  width={1280}
                  height={720}
                  alt="ImpulsaTube Pro Platform Preview"
                  className="object-cover"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <Button size="lg" variant="secondary" className="rounded-full w-16 h-16 p-0">
                    <Play className="h-8 w-8" />
                    <span className="sr-only">Play demo video</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
          <div className="absolute inset-0 -z-10 h-full w-full bg-white [background:radial-gradient(125%_125%_at_50%_10%,#fff_40%,#7c3aed_100%)]"></div>
        </section>

        {/* Features Section */}
        <section className="container px-4 md:px-6 py-16 md:py-24">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold tracking-tighter">Why Choose ImpulsaTube Pro?</h2>
            <p className="text-xl text-muted-foreground max-w-[700px] mx-auto">
              Our platform offers unique benefits for content creators and viewers alike
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon={<TrendingUp className="h-10 w-10 text-primary" />}
              title="Boost Video Visibility"
              description="Use Tubecoins to promote your videos and reach a wider audience instantly."
            />
            <FeatureCard
              icon={<Zap className="h-10 w-10 text-primary" />}
              title="Earn While Watching"
              description="Get rewarded with Tubecoins for watching promoted content and engaging with creators."
            />
            <FeatureCard
              icon={<CreditCard className="h-10 w-10 text-primary" />}
              title="Buy Tubecoins"
              description="Purchase Tubecoins with real money to supercharge your video promotion campaigns."
            />
            <FeatureCard
              icon={<Gift className="h-10 w-10 text-primary" />}
              title="Redeem Amazing Rewards"
              description="Exchange your Tubecoins for gift cards, cryptocurrency, mobile recharges, and more."
            />
            <FeatureCard
              icon={<Users className="h-10 w-10 text-primary" />}
              title="Build Your Community"
              description="Connect directly with your audience and foster a loyal community around your content."
            />
            <FeatureCard
              icon={<Play className="h-10 w-10 text-primary" />}
              title="Complete Offers"
              description="Earn extra Tubecoins by completing surveys, downloading apps, and other simple tasks."
            />
          </div>
        </section>

        {/* How It Works Section */}
        <HowItWorksSection />

        {/* Testimonials */}
        <section className="bg-muted py-16 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="text-center space-y-4 mb-16">
              <h2 className="text-3xl md:text-4xl font-bold tracking-tighter">Success Stories</h2>
              <p className="text-xl text-muted-foreground max-w-[700px] mx-auto">
                See how users are benefiting from ImpulsaTube Pro's ecosystem
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <TestimonialCard
                quote="I've earned over $200 in gift cards just by watching videos and completing offers. Amazing platform!"
                author="Alex Rodriguez"
                role="Content Creator & Viewer"
                avatarSrc="/placeholder.svg?height=100&width=100"
              />
              <TestimonialCard
                quote="ImpulsaTube Pro helped me grow my channel from 100 to 10,000 subscribers in just 3 months!"
                author="Maria Garcia"
                role="YouTube Creator"
                avatarSrc="/placeholder.svg?height=100&width=100"
              />
              <TestimonialCard
                quote="The ability to buy Tubecoins and redeem rewards makes this platform incredibly versatile."
                author="James Wilson"
                role="Tech Reviewer"
                avatarSrc="/placeholder.svg?height=100&width=100"
              />
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="container px-4 md:px-6 py-16 md:py-24">
          <div className="rounded-xl bg-primary p-8 md:p-12 shadow-lg">
            <div className="flex flex-col lg:flex-row items-center justify-between gap-8">
              <div className="space-y-4 text-center lg:text-left">
                <h2 className="text-3xl md:text-4xl font-bold tracking-tighter text-primary-foreground">
                  Ready to Start Earning and Promoting?
                </h2>
                <p className="text-xl text-primary-foreground/90 max-w-[600px]">
                  Join thousands of users already earning Tubecoins, promoting their content, and redeeming amazing
                  rewards.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" variant="secondary" className="w-full sm:w-auto" asChild>
                  <Link href="/register">Join Now - It's Free</Link>
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="w-full sm:w-auto border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary"
                  asChild
                >
                  <Link href="/login">Sign In</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <FAQSection />
      </main>
      <Footer />
    </div>
  )
}
