"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useAuth } from "@/lib/auth-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  LogOut,
  Play,
  TrendingUp,
  Coins,
  Upload,
  Gift,
  History,
  CreditCard,
  ShoppingBag,
  Users,
  User,
} from "lucide-react"
import { WatchVideosSection } from "@/components/dashboard/watch-videos-section"
import { PromoteVideosSection } from "@/components/dashboard/promote-videos-section"
import { OfferwallSection } from "@/components/dashboard/offerwall-section"
import { TransactionHistory } from "@/components/dashboard/transaction-history"
import { StatsCards } from "@/components/dashboard/stats-cards"
import { BuyTubecoinsSection } from "@/components/dashboard/buy-tubecoins-section"
import { RewardsSection } from "@/components/dashboard/rewards-section"
import { ReferralSystem } from "@/components/dashboard/referral-system"
import Link from "next/link"

export default function DashboardPage() {
  const { user, isLoading, logout } = useAuth()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    if (!isLoading && !user) {
      router.push("/login")
    }
  }, [user, isLoading, router])

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-4">
              <Avatar className="h-10 w-10 sm:h-12 sm:w-12">
                <AvatarImage src={user.avatarUrl || "/placeholder.svg?height=100&width=100"} alt={user.name} />
                <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-lg sm:text-2xl font-bold">Welcome back, {user.name}</h1>
                <p className="text-sm text-muted-foreground">Ready to earn and promote with Tubecoins?</p>
              </div>
            </div>
            <div className="flex items-center gap-2 sm:gap-4 w-full sm:w-auto">
              <Card className="px-3 py-2 flex-1 sm:flex-none">
                <div className="flex items-center gap-2">
                  <Coins className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-500" />
                  <span className="font-bold text-base sm:text-lg">{user.tubecoins}</span>
                  <span className="text-xs sm:text-sm text-muted-foreground">Tubecoins</span>
                </div>
              </Card>
              <Button variant="outline" size="sm" asChild>
                <Link href="/profile">
                  <Avatar className="h-6 w-6 mr-2">
                    <AvatarImage src={user.avatarUrl || "/placeholder.svg?height=100&width=100"} alt={user.name} />
                    <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <span className="hidden sm:inline">Profile</span>
                </Link>
              </Button>
              <Button variant="outline" size="sm" onClick={logout}>
                <LogOut className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">Sign Out</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-4 sm:py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4 sm:space-y-6">
          <TabsList className="w-full overflow-x-auto flex md:grid md:grid-cols-8 gap-1 p-1">
            <TabsTrigger
              value="overview"
              className="flex items-center gap-1 whitespace-nowrap px-3 py-2 text-xs md:text-sm"
            >
              <TrendingUp className="h-3 w-3 md:h-4 md:w-4" />
              <span className="hidden sm:inline">Overview</span>
            </TabsTrigger>
            <TabsTrigger
              value="watch"
              className="flex items-center gap-1 whitespace-nowrap px-3 py-2 text-xs md:text-sm"
            >
              <Play className="h-3 w-3 md:h-4 md:w-4" />
              <span className="hidden sm:inline">Watch</span>
            </TabsTrigger>
            <TabsTrigger
              value="promote"
              className="flex items-center gap-1 whitespace-nowrap px-3 py-2 text-xs md:text-sm"
            >
              <Upload className="h-3 w-3 md:h-4 md:w-4" />
              <span className="hidden sm:inline">Promote</span>
            </TabsTrigger>
            <TabsTrigger
              value="offers"
              className="flex items-center gap-1 whitespace-nowrap px-3 py-2 text-xs md:text-sm"
            >
              <Gift className="h-3 w-3 md:h-4 md:w-4" />
              <span className="hidden sm:inline">Offers</span>
            </TabsTrigger>
            <TabsTrigger value="buy" className="flex items-center gap-1 whitespace-nowrap px-3 py-2 text-xs md:text-sm">
              <CreditCard className="h-3 w-3 md:h-4 md:w-4" />
              <span className="hidden sm:inline">Buy</span>
            </TabsTrigger>
            <TabsTrigger
              value="rewards"
              className="flex items-center gap-1 whitespace-nowrap px-3 py-2 text-xs md:text-sm"
            >
              <ShoppingBag className="h-3 w-3 md:h-4 md:w-4" />
              <span className="hidden sm:inline">Rewards</span>
            </TabsTrigger>
            <TabsTrigger
              value="referrals"
              className="flex items-center gap-1 whitespace-nowrap px-3 py-2 text-xs md:text-sm"
            >
              <Users className="h-3 w-3 md:h-4 md:w-4" />
              <span className="hidden sm:inline">Referrals</span>
            </TabsTrigger>
            <TabsTrigger
              value="history"
              className="flex items-center gap-1 whitespace-nowrap px-3 py-2 text-xs md:text-sm"
            >
              <History className="h-3 w-3 md:h-4 md:w-4" />
              <span className="hidden sm:inline">History</span>
            </TabsTrigger>
            <TabsTrigger
              value="profile"
              className="flex items-center gap-1 whitespace-nowrap px-3 py-2 text-xs md:text-sm"
            >
              <User className="h-3 w-3 md:h-4 md:w-4" />
              <span className="hidden sm:inline">Profile</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <StatsCards user={user} />

            {/* Quick Actions */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
              <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setActiveTab("watch")}>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2">
                    <Play className="h-5 w-5 text-green-500" />
                    Watch Videos
                  </CardTitle>
                  <CardDescription>Earn Tubecoins by watching promoted content</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-green-500">+5</span>
                    <span className="text-sm text-muted-foreground">Tubecoins per video</span>
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => setActiveTab("promote")}
              >
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2">
                    <Upload className="h-5 w-5 text-blue-500" />
                    Promote Your Videos
                  </CardTitle>
                  <CardDescription>Boost your YouTube videos with Tubecoins</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-blue-500">10+</span>
                    <span className="text-sm text-muted-foreground">Views per Tubecoin</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setActiveTab("buy")}>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5 text-purple-500" />
                    Buy Tubecoins
                  </CardTitle>
                  <CardDescription>Purchase Tubecoins to boost your content</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-purple-500">$4.99</span>
                    <span className="text-sm text-muted-foreground">Starting price</span>
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => setActiveTab("referrals")}
              >
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-indigo-500" />
                    Invite Friends
                  </CardTitle>
                  <CardDescription>Earn 25 Tubecoins for each referral</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-indigo-500">+25</span>
                    <span className="text-sm text-muted-foreground">Per referral</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Your latest earnings and promotions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 rounded-full">
                        <Play className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <p className="font-medium">Watched "Amazing Tech Review"</p>
                        <p className="text-sm text-muted-foreground">2 hours ago</p>
                      </div>
                    </div>
                    <Badge variant="secondary" className="text-green-600">
                      +5 Tubecoins
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-purple-100 rounded-full">
                        <CreditCard className="h-4 w-4 text-purple-600" />
                      </div>
                      <div>
                        <p className="font-medium">Purchased Starter Pack</p>
                        <p className="text-sm text-muted-foreground">1 day ago</p>
                      </div>
                    </div>
                    <Badge variant="secondary" className="text-purple-600">
                      +100 Tubecoins
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-indigo-100 rounded-full">
                        <Users className="h-4 w-4 text-indigo-600" />
                      </div>
                      <div>
                        <p className="font-medium">Friend joined via referral</p>
                        <p className="text-sm text-muted-foreground">2 days ago</p>
                      </div>
                    </div>
                    <Badge variant="secondary" className="text-indigo-600">
                      +25 Tubecoins
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="watch">
            <WatchVideosSection user={user} />
          </TabsContent>

          <TabsContent value="promote">
            <PromoteVideosSection user={user} />
          </TabsContent>

          <TabsContent value="offers">
            <OfferwallSection user={user} />
          </TabsContent>

          <TabsContent value="buy">
            <BuyTubecoinsSection user={user} />
          </TabsContent>

          <TabsContent value="rewards">
            <RewardsSection user={user} />
          </TabsContent>

          <TabsContent value="referrals">
            <ReferralSystem user={user} />
          </TabsContent>

          <TabsContent value="history">
            <TransactionHistory user={user} />
          </TabsContent>

          <TabsContent value="profile">
            <Card>
              <CardHeader>
                <CardTitle>Profile Settings</CardTitle>
                <CardDescription>Manage your account settings and preferences</CardDescription>
              </CardHeader>
              <CardContent className="text-center py-8">
                <Avatar className="h-24 w-24 mx-auto mb-4">
                  <AvatarImage src={user.avatarUrl || "/placeholder.svg?height=100&width=100"} alt={user.name} />
                  <AvatarFallback className="text-2xl">{user.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <h3 className="text-xl font-bold mb-2">{user.name}</h3>
                <p className="text-muted-foreground mb-6">{user.email}</p>
                <Button asChild>
                  <Link href="/profile">
                    <User className="mr-2 h-4 w-4" />
                    Go to Profile Settings
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
