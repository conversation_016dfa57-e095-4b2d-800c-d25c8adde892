import { type NextRequest, NextResponse } from "next/server"
import { z } from "zod"

// Mock user database (same as in login route)
const users = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    password: "password123",
    avatarUrl: "/placeholder.svg?height=100&width=100",
    tubecoins: 500,
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    password: "password123",
    avatarUrl: "/placeholder.svg?height=100&width=100",
    tubecoins: 100,
  },
]

// Schema for registration validation
const registerSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate input
    const result = registerSchema.safeParse(body)
    if (!result.success) {
      return NextResponse.json({ error: result.error.errors[0].message }, { status: 400 })
    }

    const { name, email, password } = result.data

    // Check if user already exists
    if (users.some((u) => u.email === email)) {
      return NextResponse.json({ error: "Email already in use" }, { status: 409 })
    }

    // Create new user
    const newUser = {
      id: `${users.length + 1}`,
      name,
      email,
      password,
      avatarUrl: "/placeholder.svg?height=100&width=100",
      tubecoins: 50, // Give initial tubecoins to all users
    }

    // In a real app, you would add the user to the database here
    // users.push(newUser)

    // Return user without password
    const { password: _, ...userWithoutPassword } = newUser

    // In a real app, you would create a session or JWT token here

    return NextResponse.json({ user: userWithoutPassword }, { status: 201 })
  } catch (error) {
    console.error("Registration error:", error)
    return NextResponse.json({ error: "An unexpected error occurred" }, { status: 500 })
  }
}
