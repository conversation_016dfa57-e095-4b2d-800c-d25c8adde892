import { type NextRequest, NextResponse } from "next/server"
import { z } from "zod"

// Mock user database
const users = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    password: "password123",
    avatarUrl: "/placeholder.svg?height=100&width=100",
    tubecoins: 500,
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    password: "password123",
    avatarUrl: "/placeholder.svg?height=100&width=100",
    tubecoins: 100,
  },
]

// Schema for login validation
const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate input
    const result = loginSchema.safeParse(body)
    if (!result.success) {
      return NextResponse.json({ error: result.error.errors[0].message }, { status: 400 })
    }

    const { email, password } = result.data

    // Find user by email
    const user = users.find((u) => u.email === email)

    // Check if user exists and password matches
    if (!user || user.password !== password) {
      return NextResponse.json({ error: "Invalid email or password" }, { status: 401 })
    }

    // Return user without password
    const { password: _, ...userWithoutPassword } = user

    // In a real app, you would create a session or JWT token here

    return NextResponse.json({ user: userWithoutPassword }, { status: 200 })
  } catch (error) {
    console.error("Login error:", error)
    return NextResponse.json({ error: "An unexpected error occurred" }, { status: 500 })
  }
}
